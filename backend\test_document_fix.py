#!/usr/bin/env python3
"""
Test script to verify that the document template save/download fix works correctly.
This script tests that documents saved to Firebase Storage can be downloaded without corruption.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.document_template_service import DocumentTemplateService
from app.services.document_service import DocumentService
import requests

def test_document_save_download():
    """Test that saved documents can be downloaded correctly."""

    app, _ = create_app()  # create_app returns (app, socketio)

    with app.app_context():
        print("🧪 Testing document template save/download fix...")
        
        # Test parameters
        template_id = 18  # Onderhoudsbon template
        customer_id = 80  # Test customer
        user_id = 1       # Test user
        
        # Test form data
        form_data = {
            'datum': '2025-07-11',
            'tijd': '18:00',
            'werkzaamheden': 'Test werkzaamheden voor fix verificatie',
            'materialen': 'Test materialen',
            'opmerkingen': 'Test opmerkingen voor document fix'
        }
        
        print(f"📝 Generating document from template {template_id} for customer {customer_id}...")
        
        # Initialize services
        template_service = DocumentTemplateService()
        document_service = DocumentService()
        
        try:
            # Step 1: Generate document content (working method)
            print("1️⃣ Generating document content...")
            document_content = template_service.generate_document_content(
                template_id, customer_id, form_data
            )
            print(f"✅ Generated document: {len(document_content)} bytes")
            
            # Step 2: Save document using the fixed method
            print("2️⃣ Saving document to Firebase Storage...")
            template = template_service.get_template_by_id(template_id)
            filename = f"test_fix_{template['name']}_customer_{customer_id}.docx"
            
            saved_document = template_service.save_generated_document(
                document_content, filename, customer_id, template['document_type'], user_id
            )
            print(f"✅ Saved document with ID: {saved_document['id']}")
            
            # Step 3: Retrieve document from database
            print("3️⃣ Retrieving document from database...")
            retrieved_document = document_service.get_document_by_id(saved_document['id'])
            print(f"✅ Retrieved document: {retrieved_document['name']}")
            print(f"📄 File URL: {retrieved_document['file_url']}")
            
            # Step 4: Download document from Firebase Storage
            print("4️⃣ Downloading document from Firebase Storage...")
            response = requests.get(retrieved_document['file_url'])
            response.raise_for_status()
            downloaded_content = response.content
            print(f"✅ Downloaded document: {len(downloaded_content)} bytes")
            
            # Step 5: Compare original and downloaded content
            print("5️⃣ Comparing original and downloaded content...")
            if document_content == downloaded_content:
                print("🎉 SUCCESS: Original and downloaded content are identical!")
                print(f"📊 Content size: {len(document_content)} bytes")
                return True
            else:
                print("❌ FAILURE: Content mismatch!")
                print(f"📊 Original size: {len(document_content)} bytes")
                print(f"📊 Downloaded size: {len(downloaded_content)} bytes")
                
                # Save both files for debugging
                with open('debug_original.docx', 'wb') as f:
                    f.write(document_content)
                with open('debug_downloaded.docx', 'wb') as f:
                    f.write(downloaded_content)
                print("💾 Saved debug files: debug_original.docx and debug_downloaded.docx")
                return False
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_document_save_download()
    if success:
        print("\n🎉 Document save/download fix verification PASSED!")
        sys.exit(0)
    else:
        print("\n❌ Document save/download fix verification FAILED!")
        sys.exit(1)
