#!/usr/bin/env python3
"""
Test the improved template analysis functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_improved_template_analysis():
    """Test the improved template analysis."""
    print("=== Testing Improved Template Analysis ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Load the existing template
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        print(f"Loading template: {template_path}")
        
        # Read the template file
        with open(template_path, 'rb') as f:
            docx_content = f.read()
        
        print("✓ Template loaded successfully")
        
        # Test the enhanced field extraction
        print("\nTesting enhanced field extraction...")
        fields_info = service._extract_fields_from_docx(docx_content)
        
        print(f"\n=== ANALYSIS RESULTS ===")
        print(f"Fields found: {len(fields_info['fields'])}")
        print(f"Checkboxes found: {len(fields_info['checkboxes'])}")
        print(f"Tables found: {len(fields_info['tables'])}")
        print(f"Has structured tables: {fields_info.get('has_structured_tables', False)}")
        
        print(f"\n=== TEXT FIELDS ===")
        for i, field in enumerate(fields_info['fields'][:10], 1):  # Show first 10
            print(f"{i:2d}. {field['name']} ({field['type']}) - {field['label']}")
        if len(fields_info['fields']) > 10:
            print(f"    ... and {len(fields_info['fields']) - 10} more fields")
        
        print(f"\n=== CHECKBOXES ===")
        for i, checkbox in enumerate(fields_info['checkboxes'][:10], 1):  # Show first 10
            print(f"{i:2d}. {checkbox['name']} - {checkbox['label']}")
        if len(fields_info['checkboxes']) > 10:
            print(f"    ... and {len(fields_info['checkboxes']) - 10} more checkboxes")
        
        print(f"\n=== TABLES ===")
        for i, table in enumerate(fields_info['tables'], 1):
            print(f"\nTable {i}:")
            print(f"  Type: {table['table_type']}")
            print(f"  Has fields: {table['has_fields']}")
            print(f"  Field count: {len(table['fields'])}")
            
            if table['headers']:
                print(f"  Headers: {table['headers'][0] if table['headers'] else 'None'}")
            
            if table['fields']:
                print("  Sample fields:")
                for field in table['fields'][:3]:  # Show first 3 fields
                    print(f"    - {field['name']} (row {field['row']}, cell {field['cell']}) - {field['type']}")
                if len(table['fields']) > 3:
                    print(f"    ... and {len(table['fields']) - 3} more fields")
        
        # Determine if this template should use structured form
        should_use_structured = fields_info.get('has_structured_tables', False)
        
        print(f"\n=== STRUCTURED FORM DECISION ===")
        print(f"Should use structured form: {should_use_structured}")
        
        if should_use_structured:
            print("✓ This template will use the StructuredTemplateForm component")
            print("  - Radio buttons for Goed/Fout/NVT selections")
            print("  - Proper table layout")
            print("  - Enhanced field handling")
        else:
            print("⚠ This template will use the basic TemplateFormEditor")
            print("  - Standard form fields")
            print("  - No special table handling")
        
        # Check for specific onderhoudsbon patterns
        print(f"\n=== ONDERHOUDSBON PATTERN DETECTION ===")
        
        # Check for Goed/Fout/NVT patterns
        goed_fout_nvt_fields = [
            field for field in fields_info['fields'] + fields_info['checkboxes']
            if any(keyword in field['name'].lower() for keyword in ['goed', 'fout', 'nvt'])
        ]
        
        print(f"Goed/Fout/NVT fields found: {len(goed_fout_nvt_fields)}")
        for field in goed_fout_nvt_fields[:5]:  # Show first 5
            print(f"  - {field['name']}")
        if len(goed_fout_nvt_fields) > 5:
            print(f"  ... and {len(goed_fout_nvt_fields) - 5} more")
        
        # Check for checkbox tables
        checkbox_tables = [table for table in fields_info['tables'] if table['table_type'] == 'checkbox_table']
        print(f"Checkbox tables found: {len(checkbox_tables)}")
        
        # Check for signature tables
        signature_tables = [table for table in fields_info['tables'] if table['table_type'] == 'signature_table']
        print(f"Signature tables found: {len(signature_tables)}")
        
        # Check for form tables
        form_tables = [table for table in fields_info['tables'] if table['table_type'] == 'form_table']
        print(f"Form tables found: {len(form_tables)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during improved template analysis test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_improved_template_analysis()
    if success:
        print("\n🎉 Improved template analysis test completed!")
        sys.exit(0)
    else:
        print("\n❌ Improved template analysis test failed!")
        sys.exit(1)
