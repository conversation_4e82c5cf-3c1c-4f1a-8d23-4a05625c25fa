#!/usr/bin/env python3
"""
FULL ANALYSIS of document generation issues.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def full_document_analysis():
    """Complete analysis of document generation pipeline."""
    print("=== FULL DOCUMENT GENERATION ANALYSIS ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        from docx import Document
        import io
        
        service = DocumentTemplateService()
        
        # Load the template
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        print(f"Loading template: {template_path}")
        
        with open(template_path, 'rb') as f:
            template_content = f.read()
        
        print(f"✓ Template loaded: {len(template_content)} bytes")
        
        # Test data
        test_data = {
            'bonnummer': '2024-001',
            'telefoon': '0612345678',
            'klantnummer': '12345',
            'contactpersoon': '<PERSON>',
            'bedrijf': 'Test Bedrijf BV',
            'email': '<EMAIL>',
            'adres': 'Teststraat 123, 1234 AB Teststad',
            'type': 'Inbraakmeldsysteem',
            'inbraakmeldsysteem': True,
            'brandmeldsysteem': False,
            'cctv': False,
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False,
            'centrale_accu_opmerkingen': 'Accu spanning OK - 12.8V',
            'centrale_voeding_goed': False,
            'centrale_voeding_fout': True,
            'centrale_voeding_nvt': False,
            'centrale_voeding_opmerkingen': 'Voeding defect - moet vervangen worden',
            'klant_naam': 'Jan de Klant',
            'monteur_naam': 'Piet de Monteur',
            'datum': '11-07-2025',
            'begin_tijd': '09:00',
            'eind_tijd': '12:30',
            'klant_handtekening': '[KLANT HANDTEKENING]',
            'monteur_handtekening': '[MONTEUR HANDTEKENING]'
        }
        
        print(f"Test data prepared: {len(test_data)} fields")
        
        # TEST 1: Original template verification
        print(f"\n=== TEST 1: ORIGINAL TEMPLATE VERIFICATION ===")
        try:
            original_doc = Document(io.BytesIO(template_content))
            print(f"✓ Original template is valid DOCX")
            print(f"  - Paragraphs: {len(original_doc.paragraphs)}")
            print(f"  - Tables: {len(original_doc.tables)}")
        except Exception as e:
            print(f"✗ Original template is corrupted: {e}")
            return False
        
        # TEST 2: Safe template filling
        print(f"\n=== TEST 2: SAFE TEMPLATE FILLING ===")
        try:
            safe_result = service._fill_docx_template_safe(template_content, test_data)
            print(f"✓ Safe template filling completed: {len(safe_result)} bytes")
            
            # Verify the result
            try:
                safe_doc = Document(io.BytesIO(safe_result))
                print(f"✓ Safe result is valid DOCX")
                print(f"  - Paragraphs: {len(safe_doc.paragraphs)}")
                print(f"  - Tables: {len(safe_doc.tables)}")
                
                # Save for testing
                with open('test_safe_result.docx', 'wb') as f:
                    f.write(safe_result)
                print(f"✓ Safe result saved to: test_safe_result.docx")
                
            except Exception as e:
                print(f"✗ Safe result is corrupted: {e}")
                
        except Exception as e:
            print(f"✗ Safe template filling failed: {e}")
            import traceback
            traceback.print_exc()
        
        # TEST 3: Original template filling
        print(f"\n=== TEST 3: ORIGINAL TEMPLATE FILLING ===")
        try:
            original_result = service._fill_docx_template(template_content, test_data)
            print(f"✓ Original template filling completed: {len(original_result)} bytes")
            
            # Verify the result
            try:
                original_doc = Document(io.BytesIO(original_result))
                print(f"✓ Original result is valid DOCX")
                print(f"  - Paragraphs: {len(original_doc.paragraphs)}")
                print(f"  - Tables: {len(original_doc.tables)}")
                
                # Save for testing
                with open('test_original_result.docx', 'wb') as f:
                    f.write(original_result)
                print(f"✓ Original result saved to: test_original_result.docx")
                
            except Exception as e:
                print(f"✗ Original result is corrupted: {e}")
                
        except Exception as e:
            print(f"✗ Original template filling failed: {e}")
            import traceback
            traceback.print_exc()
        
        # TEST 4: Simple copy test
        print(f"\n=== TEST 4: SIMPLE COPY TEST ===")
        try:
            # Just copy the template without any processing
            with open('test_simple_copy.docx', 'wb') as f:
                f.write(template_content)
            
            # Verify the copy
            copy_doc = Document('test_simple_copy.docx')
            print(f"✓ Simple copy is valid DOCX")
            print(f"  - Paragraphs: {len(copy_doc.paragraphs)}")
            print(f"  - Tables: {len(copy_doc.tables)}")
            
        except Exception as e:
            print(f"✗ Simple copy failed: {e}")
        
        # TEST 5: Minimal processing test
        print(f"\n=== TEST 5: MINIMAL PROCESSING TEST ===")
        try:
            # Load template, make minimal change, save
            doc = Document(io.BytesIO(template_content))
            
            # Find first paragraph with text and make a small change
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # Just add a space - minimal change
                    paragraph.text = paragraph.text + " "
                    break
            
            # Save the minimally processed document
            output_stream = io.BytesIO()
            doc.save(output_stream)
            minimal_result = output_stream.getvalue()
            
            print(f"✓ Minimal processing completed: {len(minimal_result)} bytes")
            
            # Verify the result
            minimal_doc = Document(io.BytesIO(minimal_result))
            print(f"✓ Minimal result is valid DOCX")
            print(f"  - Paragraphs: {len(minimal_doc.paragraphs)}")
            print(f"  - Tables: {len(minimal_doc.tables)}")
            
            # Save for testing
            with open('test_minimal_result.docx', 'wb') as f:
                f.write(minimal_result)
            print(f"✓ Minimal result saved to: test_minimal_result.docx")
            
        except Exception as e:
            print(f"✗ Minimal processing failed: {e}")
            import traceback
            traceback.print_exc()
        
        # TEST 6: Check what's different
        print(f"\n=== TEST 6: DIFFERENCE ANALYSIS ===")
        
        # Compare file sizes
        files_to_check = [
            ('Original template', template_path),
            ('Safe result', 'test_safe_result.docx'),
            ('Original result', 'test_original_result.docx'),
            ('Simple copy', 'test_simple_copy.docx'),
            ('Minimal result', 'test_minimal_result.docx')
        ]
        
        print("File size comparison:")
        for name, path in files_to_check:
            if os.path.exists(path):
                size = os.path.getsize(path)
                print(f"  {name}: {size} bytes")
            else:
                print(f"  {name}: FILE NOT FOUND")
        
        # TEST 7: Try to identify the corruption point
        print(f"\n=== TEST 7: CORRUPTION POINT IDENTIFICATION ===")
        
        # Check if it's the signature processing
        test_data_no_signatures = test_data.copy()
        test_data_no_signatures['klant_handtekening'] = '[HANDTEKENING PLACEHOLDER]'
        test_data_no_signatures['monteur_handtekening'] = '[HANDTEKENING PLACEHOLDER]'
        
        try:
            no_sig_result = service._fill_docx_template_safe(template_content, test_data_no_signatures)
            print(f"✓ Template filling without signatures: {len(no_sig_result)} bytes")
            
            # Verify
            no_sig_doc = Document(io.BytesIO(no_sig_result))
            print(f"✓ No-signature result is valid DOCX")
            
            # Save for testing
            with open('test_no_signatures.docx', 'wb') as f:
                f.write(no_sig_result)
            print(f"✓ No-signature result saved to: test_no_signatures.docx")
            
        except Exception as e:
            print(f"✗ Template filling without signatures failed: {e}")
        
        print(f"\n=== ANALYSIS COMPLETE ===")
        print(f"Check the generated test files:")
        print(f"  - test_simple_copy.docx (should always work)")
        print(f"  - test_minimal_result.docx (minimal processing)")
        print(f"  - test_no_signatures.docx (without signature processing)")
        print(f"  - test_safe_result.docx (full safe processing)")
        print(f"  - test_original_result.docx (original method)")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during full document analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = full_document_analysis()
    if success:
        print("\n🔍 Full document analysis completed!")
        sys.exit(0)
    else:
        print("\n❌ Full document analysis failed!")
        sys.exit(1)
