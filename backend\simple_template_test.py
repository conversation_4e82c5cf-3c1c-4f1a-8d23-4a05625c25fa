#!/usr/bin/env python3
"""
Simple test of the enhanced template analysis without Flask context.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_template_analysis():
    """Test the enhanced template analysis directly."""
    print("=== Testing Enhanced Template Analysis (Direct) ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Load the existing template
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        print(f"Loading template: {template_path}")
        
        # Read the template file
        with open(template_path, 'rb') as f:
            docx_content = f.read()
        
        print("✓ Template loaded successfully")
        
        # Test the enhanced field extraction directly
        print("\nTesting enhanced field extraction...")
        fields_info = service._extract_fields_from_docx(docx_content)
        
        print(f"\n=== ANALYSIS RESULTS ===")
        print(f"Fields found: {len(fields_info['fields'])}")
        print(f"Checkboxes found: {len(fields_info['checkboxes'])}")
        print(f"Tables found: {len(fields_info['tables'])}")
        print(f"Has structured tables: {fields_info.get('has_structured_tables', False)}")
        
        print(f"\n=== TEXT FIELDS (first 10) ===")
        for i, field in enumerate(fields_info['fields'][:10], 1):
            table_info = ""
            if field.get('in_table'):
                table_info = f" [Table {field.get('table_index', '?')} - {field.get('table_type', 'unknown')}]"
            print(f"{i:2d}. {field['name']} ({field['type']}) - {field['label']}{table_info}")
        
        if len(fields_info['fields']) > 10:
            print(f"    ... and {len(fields_info['fields']) - 10} more fields")
        
        print(f"\n=== CHECKBOXES (first 10) ===")
        for i, checkbox in enumerate(fields_info['checkboxes'][:10], 1):
            table_info = ""
            if checkbox.get('in_table'):
                table_info = f" [Table {checkbox.get('table_index', '?')} - {checkbox.get('table_type', 'unknown')}]"
            print(f"{i:2d}. {checkbox['name']} - {checkbox['label']}{table_info}")
        
        if len(fields_info['checkboxes']) > 10:
            print(f"    ... and {len(fields_info['checkboxes']) - 10} more checkboxes")
        
        print(f"\n=== TABLES SUMMARY ===")
        table_types = {}
        for table in fields_info['tables']:
            table_type = table['table_type']
            if table_type not in table_types:
                table_types[table_type] = 0
            table_types[table_type] += 1
        
        for table_type, count in table_types.items():
            print(f"  - {table_type}: {count} tables")
        
        print(f"\n=== DETAILED TABLE ANALYSIS (first 5 tables) ===")
        for i, table in enumerate(fields_info['tables'][:5], 1):
            print(f"\n{i:2d}. Table {table['index']} - Type: {table['table_type']}")
            print(f"    Has fields: {table['has_fields']}")
            print(f"    Fields in table: {len(table['fields'])}")
            
            if table.get('headers'):
                print(f"    Headers: {table['headers'][:2]}")  # Show first 2 header rows
            
            if table.get('structure'):
                print(f"    Structure: {len(table['structure'])} rows")
                header_rows = sum(1 for row in table['structure'] if row.get('is_header'))
                data_rows = len(table['structure']) - header_rows
                print(f"    Header rows: {header_rows}, Data rows: {data_rows}")
            
            if table['fields']:
                print("    Sample fields:")
                for field in table['fields'][:3]:  # Show first 3 fields
                    print(f"      - {field['name']} (row {field['row']}, cell {field['cell']}) - {field['type']}")
                if len(table['fields']) > 3:
                    print(f"      ... and {len(table['fields']) - 3} more fields")
        
        if len(fields_info['tables']) > 5:
            print(f"\n    ... and {len(fields_info['tables']) - 5} more tables")
        
        # Determine if this template should use structured form
        should_use_structured = fields_info.get('has_structured_tables', False) or \
            any(table['table_type'] in ['checkbox_table', 'signature_table', 'form_table'] for table in fields_info['tables'])
        
        print(f"\n=== RECOMMENDATION ===")
        print(f"Should use structured form: {should_use_structured}")
        
        structured_tables = [t for t in fields_info['tables'] if t['table_type'] in ['checkbox_table', 'signature_table', 'form_table']]
        if structured_tables:
            print(f"Structured tables found: {len(structured_tables)}")
            for table in structured_tables:
                print(f"  - Table {table['index']}: {table['table_type']} ({len(table['fields'])} fields)")
        
        if should_use_structured:
            print("✓ This template has proper table structure and will use the new structured form display")
        else:
            print("ℹ This template will use the traditional field-by-field display")
        
        print("\n✓ Enhanced template analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error during template analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_template_analysis()
    if success:
        print("\n🎉 Analysis completed!")
        sys.exit(0)
    else:
        print("\n❌ Analysis failed!")
        sys.exit(1)
