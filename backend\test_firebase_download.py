#!/usr/bin/env python3
"""
Test script to debug Firebase Storage download issues.
This script tests different methods of downloading files from Firebase Storage.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.document_service import DocumentService
import requests
from firebase_admin import storage

def test_firebase_download_methods():
    """Test different methods of downloading from Firebase Storage."""
    
    app, _ = create_app()
    
    with app.app_context():
        print("🧪 Testing Firebase Storage download methods...")
        
        # Get a recent document
        document_service = DocumentService()
        
        try:
            # Get a recent document from the database
            from app.models.document import Document
            recent_doc = Document.query.order_by(Document.created_at.desc()).first()

            if not recent_doc:
                print("❌ No documents found in database")
                return False
            print(f"📄 Testing with document: {recent_doc.name}")
            print(f"🔗 Firebase URL: {recent_doc.file_url}")
            print(f"📁 Storage path: {recent_doc.file_path}")
            
            # Method 1: Direct requests.get (current problematic method)
            print("\n1️⃣ Testing direct requests.get (current method)...")
            try:
                response = requests.get(recent_doc.file_url)
                response.raise_for_status()
                requests_content = response.content
                print(f"✅ requests.get: {len(requests_content)} bytes")
                print(f"📋 Content-Type: {response.headers.get('content-type', 'unknown')}")
                
                # Save for inspection
                with open('debug_requests_download.docx', 'wb') as f:
                    f.write(requests_content)
                print("💾 Saved as debug_requests_download.docx")
                
            except Exception as e:
                print(f"❌ requests.get failed: {e}")
                requests_content = None
            
            # Method 2: Direct Firebase blob download
            print("\n2️⃣ Testing direct Firebase blob download...")
            try:
                bucket = storage.bucket()
                blob = bucket.blob(recent_doc.file_path)
                
                if blob.exists():
                    firebase_content = blob.download_as_bytes()
                    print(f"✅ Firebase blob: {len(firebase_content)} bytes")
                    
                    # Save for inspection
                    with open('debug_firebase_download.docx', 'wb') as f:
                        f.write(firebase_content)
                    print("💾 Saved as debug_firebase_download.docx")
                    
                    # Get blob metadata
                    blob.reload()
                    print(f"📋 Blob Content-Type: {blob.content_type}")
                    print(f"📋 Blob Size: {blob.size}")
                    
                else:
                    print(f"❌ Blob does not exist at path: {recent_doc.file_path}")
                    firebase_content = None
                    
            except Exception as e:
                print(f"❌ Firebase blob download failed: {e}")
                firebase_content = None
            
            # Method 3: requests.get with proper headers
            print("\n3️⃣ Testing requests.get with proper headers...")
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/octet-stream,*/*',
                    'Accept-Encoding': 'identity'  # Disable compression
                }
                response = requests.get(recent_doc.file_url, headers=headers)
                response.raise_for_status()
                headers_content = response.content
                print(f"✅ requests.get with headers: {len(headers_content)} bytes")
                
                # Save for inspection
                with open('debug_headers_download.docx', 'wb') as f:
                    f.write(headers_content)
                print("💾 Saved as debug_headers_download.docx")
                
            except Exception as e:
                print(f"❌ requests.get with headers failed: {e}")
                headers_content = None
            
            # Compare results
            print("\n🔍 Comparing results...")
            if requests_content and firebase_content:
                if requests_content == firebase_content:
                    print("✅ requests.get and Firebase blob content are IDENTICAL")
                else:
                    print("❌ requests.get and Firebase blob content are DIFFERENT")
                    print(f"📊 requests.get size: {len(requests_content)}")
                    print(f"📊 Firebase blob size: {len(firebase_content)}")
                    
                    # Check first few bytes
                    print(f"🔍 requests.get first 50 bytes: {requests_content[:50]}")
                    print(f"🔍 Firebase blob first 50 bytes: {firebase_content[:50]}")
            
            if headers_content and firebase_content:
                if headers_content == firebase_content:
                    print("✅ requests.get with headers and Firebase blob content are IDENTICAL")
                else:
                    print("❌ requests.get with headers and Firebase blob content are DIFFERENT")
            
            # Test if files are valid DOCX
            print("\n📋 Testing file validity...")
            for name, content in [
                ("requests.get", requests_content),
                ("Firebase blob", firebase_content), 
                ("requests with headers", headers_content)
            ]:
                if content:
                    try:
                        from docx import Document
                        import io
                        doc = Document(io.BytesIO(content))
                        print(f"✅ {name}: Valid DOCX with {len(doc.paragraphs)} paragraphs")
                    except Exception as e:
                        print(f"❌ {name}: Invalid DOCX - {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_firebase_download_methods()
    if success:
        print("\n🎉 Firebase download test completed!")
    else:
        print("\n❌ Firebase download test failed!")
