"""
Time Tracking service module.
This module defines the service for time and mileage entry operations.
"""
from app.repositories.time_entry_repository import TimeEntryRepository
from app.repositories.mileage_entry_repository import MileageEntryRepository
from app.repositories.user_repository import UserRepository
from app.services.audit_service import AuditService
from app.utils.cache_utils import clear_cache_for_entity
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
import calendar

class TimeTrackingService:
    """Service for time and mileage entry operations."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.time_entry_repo = TimeEntryRepository()
        self.mileage_entry_repo = MileageEntryRepository()
        self.user_repo = UserRepository()
        self.audit_service = AuditService()

    # Time Entry Methods

    def get_time_entries(self, page: int = 1, per_page: int = 20) -> Dict:
        """Get all time entries with pagination."""
        entries, total = self.time_entry_repo.get_all(page, per_page)
        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def get_time_entry_by_id(self, entry_id: int) -> Dict:
        """Get a time entry by ID."""
        entry = self.time_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Time entry not found")
        return entry.to_dict()

    def get_time_entries_by_user(self, user_id: int, page: int = 1, per_page: int = 20) -> Dict:
        """Get time entries for a specific user with pagination."""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        entries, total = self.time_entry_repo.get_by_user_id(user_id, page, per_page)
        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def get_time_entries_by_month_year(self, month: int, year: int, user_id: Optional[int] = None, page: int = 1, per_page: int = 20) -> Dict:
        """Get time entries for a specific month and year for a specific user (optional)."""
        if user_id:
            user = self.user_repo.get_by_id(user_id)
            if not user:
                raise Exception("User not found")

        entries, total = self.time_entry_repo.get_by_month_year(month, year, user_id, page, per_page)

        # Get monthly hours
        monthly_total_hours = 0
        monthly_approved_hours = 0
        monthly_pending_hours = 0
        if user_id:
            monthly_total_hours = self.time_entry_repo.get_monthly_total_hours(user_id, month, year)
            monthly_approved_hours = self.time_entry_repo.get_monthly_approved_hours(user_id, month, year)
            monthly_pending_hours = self.time_entry_repo.get_monthly_pending_hours(user_id, month, year)

        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page,
            "month": month,
            "year": year,
            "monthly_total_hours": monthly_total_hours,
            "monthly_approved_hours": monthly_approved_hours,
            "monthly_pending_hours": monthly_pending_hours
        }

    def get_pending_time_entries(self, page: int = 1, per_page: int = 20) -> Dict:
        """Get pending time entries with pagination."""
        entries, total = self.time_entry_repo.get_by_status("pending", page, per_page)
        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def create_time_entry(self, user_id: int, date_str: str, start_time_str: str, end_time_str: str, break_time: int = 0, description: Optional[str] = None, current_user_id: Optional[int] = None, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Create a new time entry."""
        # Validate user
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        # Parse date and times
        try:
            entry_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            start_time = datetime.strptime(start_time_str, "%H:%M").time()
            end_time = datetime.strptime(end_time_str, "%H:%M").time()
        except ValueError:
            raise Exception("Invalid date or time format")

        # Validate end_time is after start_time
        if end_time <= start_time:
            raise Exception("End time must be after start time")

        # Validate break_time
        if break_time < 0 or break_time > 480:  # Max 8 hours break
            raise Exception("Break time must be between 0 and 480 minutes")

        # Create the entry
        entry = self.time_entry_repo.create(user_id, entry_date, start_time, end_time, break_time, description)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='create_time_entry',
            entity_type='time_entry',
            entity_id=entry.id,
            details={
                'user_id': user_id,
                'date': date_str,
                'start_time': start_time_str,
                'end_time': end_time_str,
                'break_time': break_time,
                'description': description
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('time_entries')

        return entry.to_dict()

    def update_time_entry(self, entry_id: int, date_str: Optional[str] = None, start_time_str: Optional[str] = None, end_time_str: Optional[str] = None, break_time: Optional[int] = None, description: Optional[str] = None, current_user_id: Optional[int] = None, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Update a time entry."""
        # Get the entry
        entry = self.time_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Time entry not found")

        # Check if entry is already approved
        if entry.status == "approved":
            raise Exception("Cannot update an approved time entry")

        # Parse date and times if provided
        entry_date = None
        start_time = None
        end_time = None

        if date_str:
            try:
                entry_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                raise Exception("Invalid date format")

        if start_time_str:
            try:
                start_time = datetime.strptime(start_time_str, "%H:%M").time()
            except ValueError:
                raise Exception("Invalid start time format")

        if end_time_str:
            try:
                end_time = datetime.strptime(end_time_str, "%H:%M").time()
            except ValueError:
                raise Exception("Invalid end time format")

        # Validate end_time is after start_time if both are provided
        if start_time and end_time and end_time <= start_time:
            raise Exception("End time must be after start time")

        # If only one time is provided, use the existing value for validation
        if start_time and not end_time:
            if start_time >= entry.end_time:
                raise Exception("Start time must be before end time")

        if end_time and not start_time:
            if end_time <= entry.start_time:
                raise Exception("End time must be after start time")

        # Update the entry
        entry = self.time_entry_repo.update(entry, entry_date, start_time, end_time, break_time, description)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='update_time_entry',
            entity_type='time_entry',
            entity_id=entry.id,
            details={
                'date': date_str,
                'start_time': start_time_str,
                'end_time': end_time_str,
                'break_time': break_time,
                'description': description
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('time_entries')

        return entry.to_dict()

    def approve_time_entry(self, entry_id: int, approver_id: int, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Approve a time entry."""
        # Get the entry
        entry = self.time_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Time entry not found")

        # Check if entry is already approved or rejected
        if entry.status != "pending":
            raise Exception(f"Time entry is already {entry.status}")

        # Get the approver
        approver = self.user_repo.get_by_id(approver_id)
        if not approver:
            raise Exception("Approver not found")

        # Check if approver is an administrator
        if approver.role != "administrator":
            raise Exception("Only administrators can approve time entries")

        # Approve the entry
        entry = self.time_entry_repo.approve(entry, approver_id)

        # Log the action
        self.audit_service.log_action(
            user_id=approver_id,
            action='approve_time_entry',
            entity_type='time_entry',
            entity_id=entry.id,
            details={
                'user_id': entry.user_id,
                'date': entry.date.isoformat(),
                'status': 'approved'
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('time_entries')

        return entry.to_dict()

    def reject_time_entry(self, entry_id: int, approver_id: int, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Reject a time entry."""
        # Get the entry
        entry = self.time_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Time entry not found")

        # Check if entry is already approved or rejected
        if entry.status != "pending":
            raise Exception(f"Time entry is already {entry.status}")

        # Get the approver
        approver = self.user_repo.get_by_id(approver_id)
        if not approver:
            raise Exception("Approver not found")

        # Check if approver is an administrator
        if approver.role != "administrator":
            raise Exception("Only administrators can reject time entries")

        # Reject the entry
        entry = self.time_entry_repo.reject(entry, approver_id)

        # Log the action
        self.audit_service.log_action(
            user_id=approver_id,
            action='reject_time_entry',
            entity_type='time_entry',
            entity_id=entry.id,
            details={
                'user_id': entry.user_id,
                'date': entry.date.isoformat(),
                'status': 'rejected'
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('time_entries')

        return entry.to_dict()

    def delete_time_entry(self, entry_id: int, current_user_id: Optional[int] = None, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Delete a time entry."""
        # Get the entry
        entry = self.time_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Time entry not found")

        # Check if entry is already approved
        if entry.status == "approved":
            raise Exception("Cannot delete an approved time entry")

        # Store entry details for logging
        entry_details = entry.to_dict()

        # Delete the entry
        self.time_entry_repo.delete(entry)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='delete_time_entry',
            entity_type='time_entry',
            entity_id=entry_id,
            details=entry_details,
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('time_entries')

        return {"message": "Time entry deleted successfully"}

    def check_time_entry_overlap(self, user_id: int, date_str: str, start_time_str: str, end_time_str: str, exclude_entry_id: Optional[int] = None) -> Dict:
        """
        Check if a time entry overlaps with existing entries.

        Args:
            user_id: The ID of the user.
            date_str: The date of the entry (YYYY-MM-DD).
            start_time_str: The start time of the entry (HH:MM).
            end_time_str: The end time of the entry (HH:MM).
            exclude_entry_id: Optional ID of the entry to exclude from the check.

        Returns:
            Dict with overlaps flag and list of overlapping entries.
        """
        # Parse date and times
        try:
            entry_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            start_time = datetime.strptime(start_time_str, "%H:%M").time()
            end_time = datetime.strptime(end_time_str, "%H:%M").time()
        except ValueError:
            raise Exception("Invalid date or time format")

        # Validate end_time is after start_time
        if end_time <= start_time:
            raise Exception("End time must be after start time")

        # Get all entries for this user on this date
        entries = self.time_entry_repo.get_by_user_date(user_id, entry_date)

        # Filter out the entry being edited if provided
        if exclude_entry_id:
            entries = [entry for entry in entries if entry.id != exclude_entry_id]

        # Check for overlaps
        overlapping_entries = []
        for entry in entries:
            # Two time ranges overlap if one starts before the other ends and ends after the other starts
            if (start_time < entry.end_time and end_time > entry.start_time):
                entry_dict = entry.to_dict()
                # Add to list of overlapping entries
                overlapping_entries.append(entry_dict)

        has_overlaps = len(overlapping_entries) > 0

        # Log the result for debugging
        print(f"Overlap check for user {user_id} on {date_str} from {start_time_str} to {end_time_str}: {has_overlaps}")
        if has_overlaps:
            print(f"Overlapping entries: {overlapping_entries}")

        return {
            "overlaps": has_overlaps,
            "entries": overlapping_entries
        }

    # Mileage Entry Methods

    def get_mileage_entries(self, page: int = 1, per_page: int = 20) -> Dict:
        """Get all mileage entries with pagination."""
        entries, total = self.mileage_entry_repo.get_all(page, per_page)
        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def get_mileage_entry_by_id(self, entry_id: int) -> Dict:
        """Get a mileage entry by ID."""
        entry = self.mileage_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Mileage entry not found")
        return entry.to_dict()

    def get_mileage_entries_by_user(self, user_id: int, page: int = 1, per_page: int = 20) -> Dict:
        """Get mileage entries for a specific user with pagination."""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        entries, total = self.mileage_entry_repo.get_by_user_id(user_id, page, per_page)
        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def get_mileage_entries_by_month_year(self, month: int, year: int, user_id: Optional[int] = None, page: int = 1, per_page: int = 20) -> Dict:
        """Get mileage entries for a specific month and year for a specific user (optional)."""
        if user_id:
            user = self.user_repo.get_by_id(user_id)
            if not user:
                raise Exception("User not found")

        entries, total = self.mileage_entry_repo.get_by_month_year(month, year, user_id, page, per_page)

        # Get monthly kilometers
        monthly_total_kilometers = 0
        monthly_approved_kilometers = 0
        monthly_pending_kilometers = 0
        if user_id:
            monthly_total_kilometers = self.mileage_entry_repo.get_monthly_total_kilometers(user_id, month, year)
            monthly_approved_kilometers = self.mileage_entry_repo.get_monthly_approved_kilometers(user_id, month, year)
            monthly_pending_kilometers = self.mileage_entry_repo.get_monthly_pending_kilometers(user_id, month, year)

        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page,
            "month": month,
            "year": year,
            "monthly_total_kilometers": monthly_total_kilometers,
            "monthly_approved_kilometers": monthly_approved_kilometers,
            "monthly_pending_kilometers": monthly_pending_kilometers
        }

    def get_pending_mileage_entries(self, page: int = 1, per_page: int = 20) -> Dict:
        """Get pending mileage entries with pagination."""
        entries, total = self.mileage_entry_repo.get_by_status("pending", page, per_page)
        return {
            "entries": [entry.to_dict() for entry in entries],
            "total": total,
            "page": page,
            "per_page": per_page
        }

    def create_mileage_entry(self, user_id: int, date_str: str, license_plate: str, start_odometer: int, end_odometer: int, reason: str, description: Optional[str] = None, current_user_id: Optional[int] = None, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Create a new mileage entry."""
        # Validate user
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        # Parse date
        try:
            entry_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            raise Exception("Invalid date format")

        # Validate odometer readings
        if start_odometer < 0:
            raise Exception("Start odometer reading must be non-negative")
        if end_odometer < 0:
            raise Exception("End odometer reading must be non-negative")
        if end_odometer <= start_odometer:
            raise Exception("End odometer reading must be greater than start odometer reading")

        # Calculate kilometers
        kilometers = float(end_odometer - start_odometer)

        # Validate required fields
        if not license_plate.strip():
            raise Exception("License plate is required")
        if not reason.strip():
            raise Exception("Reason is required")

        # Create the entry
        entry = self.mileage_entry_repo.create(user_id, entry_date, license_plate, start_odometer, end_odometer, kilometers, reason, description)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='create_mileage_entry',
            entity_type='mileage_entry',
            entity_id=entry.id,
            details={
                'user_id': user_id,
                'date': date_str,
                'license_plate': license_plate,
                'start_odometer': start_odometer,
                'end_odometer': end_odometer,
                'kilometers': kilometers,
                'reason': reason,
                'description': description
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('mileage_entries')

        return entry.to_dict()

    def update_mileage_entry(self, entry_id: int, date_str: Optional[str] = None, license_plate: Optional[str] = None, start_odometer: Optional[int] = None, end_odometer: Optional[int] = None, reason: Optional[str] = None, description: Optional[str] = None, current_user_id: Optional[int] = None, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Update a mileage entry."""
        # Get the entry
        entry = self.mileage_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Mileage entry not found")

        # Check if entry is already approved
        if entry.status == "approved":
            raise Exception("Cannot update an approved mileage entry")

        # Parse date if provided
        entry_date = None
        if date_str:
            try:
                entry_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                raise Exception("Invalid date format")

        # Validate odometer readings if provided
        if start_odometer is not None and start_odometer < 0:
            raise Exception("Start odometer reading must be non-negative")
        if end_odometer is not None and end_odometer < 0:
            raise Exception("End odometer reading must be non-negative")

        # Calculate kilometers if both odometer readings are provided
        kilometers = None
        if start_odometer is not None and end_odometer is not None:
            if end_odometer <= start_odometer:
                raise Exception("End odometer reading must be greater than start odometer reading")
            kilometers = float(end_odometer - start_odometer)
        elif start_odometer is not None and entry.end_odometer:
            if entry.end_odometer <= start_odometer:
                raise Exception("End odometer reading must be greater than start odometer reading")
            kilometers = float(entry.end_odometer - start_odometer)
        elif end_odometer is not None and entry.start_odometer:
            if end_odometer <= entry.start_odometer:
                raise Exception("End odometer reading must be greater than start odometer reading")
            kilometers = float(end_odometer - entry.start_odometer)

        # Update the entry
        entry = self.mileage_entry_repo.update(entry, entry_date, license_plate, start_odometer, end_odometer, kilometers, reason, description)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='update_mileage_entry',
            entity_type='mileage_entry',
            entity_id=entry.id,
            details={
                'date': date_str,
                'license_plate': license_plate,
                'start_odometer': start_odometer,
                'end_odometer': end_odometer,
                'kilometers': kilometers,
                'reason': reason,
                'description': description
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('mileage_entries')

        return entry.to_dict()

    def approve_mileage_entry(self, entry_id: int, approver_id: int, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Approve a mileage entry."""
        # Get the entry
        entry = self.mileage_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Mileage entry not found")

        # Check if entry is already approved or rejected
        if entry.status != "pending":
            raise Exception(f"Mileage entry is already {entry.status}")

        # Get the approver
        approver = self.user_repo.get_by_id(approver_id)
        if not approver:
            raise Exception("Approver not found")

        # Check if approver is an administrator
        if approver.role != "administrator":
            raise Exception("Only administrators can approve mileage entries")

        # Approve the entry
        entry = self.mileage_entry_repo.approve(entry, approver_id)

        # Log the action
        self.audit_service.log_action(
            user_id=approver_id,
            action='approve_mileage_entry',
            entity_type='mileage_entry',
            entity_id=entry.id,
            details={
                'user_id': entry.user_id,
                'date': entry.date.isoformat(),
                'status': 'approved'
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('mileage_entries')

        return entry.to_dict()

    def reject_mileage_entry(self, entry_id: int, approver_id: int, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Reject a mileage entry."""
        # Get the entry
        entry = self.mileage_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Mileage entry not found")

        # Check if entry is already approved or rejected
        if entry.status != "pending":
            raise Exception(f"Mileage entry is already {entry.status}")

        # Get the approver
        approver = self.user_repo.get_by_id(approver_id)
        if not approver:
            raise Exception("Approver not found")

        # Check if approver is an administrator
        if approver.role != "administrator":
            raise Exception("Only administrators can reject mileage entries")

        # Reject the entry
        entry = self.mileage_entry_repo.reject(entry, approver_id)

        # Log the action
        self.audit_service.log_action(
            user_id=approver_id,
            action='reject_mileage_entry',
            entity_type='mileage_entry',
            entity_id=entry.id,
            details={
                'user_id': entry.user_id,
                'date': entry.date.isoformat(),
                'status': 'rejected'
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('mileage_entries')

        return entry.to_dict()

    def delete_mileage_entry(self, entry_id: int, current_user_id: Optional[int] = None, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Dict:
        """Delete a mileage entry."""
        # Get the entry
        entry = self.mileage_entry_repo.get_by_id(entry_id)
        if not entry:
            raise Exception("Mileage entry not found")

        # Check if entry is already approved
        if entry.status == "approved":
            raise Exception("Cannot delete an approved mileage entry")

        # Store entry details for logging
        entry_details = entry.to_dict()

        # Delete the entry
        self.mileage_entry_repo.delete(entry)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='delete_mileage_entry',
            entity_type='mileage_entry',
            entity_id=entry_id,
            details=entry_details,
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('mileage_entries')

        return {"message": "Mileage entry deleted successfully"}

    # Combined Methods

    def get_monthly_summary(self, user_id: int, year: int) -> Dict:
        """Get a summary of time and mileage entries for each month of a year for a specific user."""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        summary = []
        for month in range(1, 13):
            # Get hours for the month
            total_hours = self.time_entry_repo.get_monthly_total_hours(user_id, month, year)
            approved_hours = self.time_entry_repo.get_monthly_approved_hours(user_id, month, year)
            pending_hours = self.time_entry_repo.get_monthly_pending_hours(user_id, month, year)

            # Get kilometers for the month
            total_kilometers = self.mileage_entry_repo.get_monthly_total_kilometers(user_id, month, year)
            approved_kilometers = self.mileage_entry_repo.get_monthly_approved_kilometers(user_id, month, year)
            pending_kilometers = self.mileage_entry_repo.get_monthly_pending_kilometers(user_id, month, year)

            # Get month name
            month_name = calendar.month_name[month]

            summary.append({
                "month": month,
                "month_name": month_name,
                "year": year,
                "total_hours": total_hours,
                "approved_hours": approved_hours,
                "pending_hours": pending_hours,
                "total_kilometers": total_kilometers,
                "approved_kilometers": approved_kilometers,
                "pending_kilometers": pending_kilometers
            })

        return {
            "user_id": user_id,
            "user_name": user.name if user.name else user.email,
            "year": year,
            "monthly_summary": summary
        }
