#!/usr/bin/env python3
"""
Test script to verify the safe template filling functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_safe_template_filling():
    """Test the safe template filling with real data."""
    print("=== Testing Safe Template Filling ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Load the existing template
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        print(f"Loading template: {template_path}")
        
        # Read the template file
        with open(template_path, 'rb') as f:
            template_content = f.read()
        
        print("✓ Template loaded successfully")
        
        # Create comprehensive test data based on actual template fields
        test_data = {
            # Basic info (Table 0)
            'bonnummer': '2024-001',
            'telefoon': '0612345678',
            'klantnummer': '12345',
            'contactpersoon': '<PERSON>',
            'bedrijf': 'Test Bedrijf BV',
            'email': '<EMAIL>',
            'adres': 'Teststraat 123, 1234 AB Teststad',
            'type': 'Inbraakmeldsysteem',

            # System types (Paragraph checkboxes)
            'inbraakmeldsysteem': True,
            'brandmeldsysteem': False,
            'cctv': False,

            # Centrale - Accu (Table 1, Row 1)
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False,
            'centrale_accu_opmerkingen': 'Accu spanning OK - 12.8V',

            # Centrale - Voeding (Table 1, Row 2)
            'centrale_voeding_goed': False,
            'centrale_voeding_fout': True,
            'centrale_voeding_nvt': False,
            'centrale_voeding_opmerkingen': 'Voeding defect - moet vervangen worden',

            # Centrale - Lusspanning (Table 1, Row 3)
            'centrale_lusspanning_goed': True,
            'centrale_lusspanning_fout': False,
            'centrale_lusspanning_nvt': False,
            'centrale_lusspanning_opmerkingen': 'Alle lussen OK',

            # Centrale - Uitlezing (Table 1, Row 4)
            'centrale_uitlezing_goed': False,
            'centrale_uitlezing_fout': False,
            'centrale_uitlezing_nvt': True,
            'centrale_uitlezing_opmerkingen': 'Niet van toepassing',

            # Centrale - Algemeen (Table 1, Row 5)
            'centrale_algemeen_goed': True,
            'centrale_algemeen_fout': False,
            'centrale_algemeen_nvt': False,
            'centrale_algemeen_opmerkingen': 'Systeem functioneert correct',

            # Signatures (Table 2)
            'klant_naam': 'Jan de Klant',
            'monteur_naam': 'Piet de Monteur',
            'datum': '11-07-2025',
            'begin_tijd': '09:00',
            'eind_tijd': '12:30',
            'klant_handtekening': '[KLANT HANDTEKENING]',
            'monteur_handtekening': '[MONTEUR HANDTEKENING]'
        }
        
        print(f"Test data prepared: {len(test_data)} fields")
        
        # Test the safe template filling
        print("\nTesting safe template filling...")
        
        try:
            filled_document = service._fill_docx_template_safe(template_content, test_data)
            print("✓ Safe template filling completed successfully")
            
            # Verify the output is valid
            if len(filled_document) > 0:
                print(f"✓ Generated document size: {len(filled_document)} bytes")
                
                # Try to save and verify the document
                output_path = 'test_output_safe.docx'
                with open(output_path, 'wb') as f:
                    f.write(filled_document)
                
                print(f"✓ Document saved to: {output_path}")
                
                # Try to read it back to verify it's not corrupted
                try:
                    from docx import Document
                    import io
                    
                    # Test if the document can be opened
                    doc = Document(io.BytesIO(filled_document))
                    paragraph_count = len(doc.paragraphs)
                    table_count = len(doc.tables)
                    
                    print(f"✓ Document verification successful:")
                    print(f"  - Paragraphs: {paragraph_count}")
                    print(f"  - Tables: {table_count}")
                    
                    # Check if some of our data is in the document
                    full_text = '\n'.join([p.text for p in doc.paragraphs])
                    
                    # Check for some key values
                    checks = [
                        ('bonnummer', '2024-001'),
                        ('bedrijf', 'Test Bedrijf BV'),
                        ('monteur', 'Piet de Monteur'),
                        ('opmerkingen', 'Accu spanning OK')
                    ]
                    
                    print(f"\n✓ Content verification:")
                    for check_name, check_value in checks:
                        if check_value in full_text:
                            print(f"  ✓ {check_name}: Found '{check_value}'")
                        else:
                            print(f"  ⚠ {check_name}: '{check_value}' not found")
                    
                    return True
                    
                except Exception as e:
                    print(f"✗ Document verification failed: {e}")
                    return False
            else:
                print("✗ Generated document is empty")
                return False
                
        except Exception as e:
            print(f"✗ Safe template filling failed: {e}")
            
            # Try fallback method
            print("\nTrying fallback method...")
            try:
                filled_document = service._fill_docx_template(template_content, test_data)
                print("✓ Fallback template filling completed")
                
                if len(filled_document) > 0:
                    output_path = 'test_output_fallback.docx'
                    with open(output_path, 'wb') as f:
                        f.write(filled_document)
                    print(f"✓ Fallback document saved to: {output_path}")
                    return True
                else:
                    print("✗ Fallback document is empty")
                    return False
                    
            except Exception as e2:
                print(f"✗ Fallback method also failed: {e2}")
                return False
        
    except Exception as e:
        print(f"✗ Error during safe template filling test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_safe_template_filling()
    if success:
        print("\n🎉 Safe template filling test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Safe template filling test failed!")
        sys.exit(1)
