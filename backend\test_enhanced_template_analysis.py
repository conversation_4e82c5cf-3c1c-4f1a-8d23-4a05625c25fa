#!/usr/bin/env python3
"""
Test script for enhanced template analysis functionality.
This script tests the improved table structure analysis.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_analysis():
    """Test the enhanced template analysis with table structure detection."""
    print("=== Testing Enhanced Template Analysis ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        from app import create_app
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            service = DocumentTemplateService()
            
            # Test with a sample DOCX content that has table structure
            print("Creating test DOCX with table structure...")
            
            # Create a simple test DOCX with table structure
            from docx import Document
            from docx.shared import Inches
            import io
            
            doc = Document()
            doc.add_heading('Onderhoudsbon Test', 0)
            
            # Add basic info table
            basic_table = doc.add_table(rows=4, cols=2)
            basic_table.style = 'Table Grid'
            
            cells = basic_table.rows[0].cells
            cells[0].text = 'Bonnummer:'
            cells[1].text = '{bonnummer}'
            
            cells = basic_table.rows[1].cells
            cells[0].text = 'Klantnummer:'
            cells[1].text = '{klantnummer}'
            
            cells = basic_table.rows[2].cells
            cells[0].text = 'Bedrijf:'
            cells[1].text = '{bedrijf}'
            
            cells = basic_table.rows[3].cells
            cells[0].text = 'Adres:'
            cells[1].text = '{adres}'
            
            # Add checkbox table
            doc.add_heading('Type installatie', level=1)
            checkbox_table = doc.add_table(rows=4, cols=4)
            checkbox_table.style = 'Table Grid'
            
            # Headers
            headers = checkbox_table.rows[0].cells
            headers[0].text = 'Component'
            headers[1].text = 'Goed'
            headers[2].text = 'Fout'
            headers[3].text = 'Opmerkingen'
            
            # Data rows
            rows_data = [
                ('Accu', '{#accu_goed}', '{#accu_fout}', '{accu_opmerkingen}'),
                ('Voeding', '{#voeding_goed}', '{#voeding_fout}', '{voeding_opmerkingen}'),
                ('Lusspanning', '{#lusspanning_goed}', '{#lusspanning_fout}', '{lusspanning_opmerkingen}')
            ]
            
            for i, (component, goed, fout, opmerkingen) in enumerate(rows_data, 1):
                cells = checkbox_table.rows[i].cells
                cells[0].text = component
                cells[1].text = goed
                cells[2].text = fout
                cells[3].text = opmerkingen
            
            # Add signature table
            doc.add_heading('Akkoord', level=1)
            signature_table = doc.add_table(rows=3, cols=2)
            signature_table.style = 'Table Grid'
            
            cells = signature_table.rows[0].cells
            cells[0].text = 'Akkoord opdrachtgever'
            cells[1].text = 'Monteur'
            
            cells = signature_table.rows[1].cells
            cells[0].text = 'Naam: {klant_naam}'
            cells[1].text = 'Naam: {monteur_naam}'
            
            cells = signature_table.rows[2].cells
            cells[0].text = 'Handtekening: {klant_handtekening}'
            cells[1].text = 'Handtekening: {monteur_handtekening}'
            
            # Save to bytes
            docx_buffer = io.BytesIO()
            doc.save(docx_buffer)
            docx_content = docx_buffer.getvalue()
            
            print("✓ Test DOCX created successfully")
            
            # Test the enhanced field extraction
            print("\nTesting enhanced field extraction...")
            fields_info = service._extract_fields_from_docx(docx_content)
            
            print(f"\n=== ANALYSIS RESULTS ===")
            print(f"Fields found: {len(fields_info['fields'])}")
            print(f"Checkboxes found: {len(fields_info['checkboxes'])}")
            print(f"Tables found: {len(fields_info['tables'])}")
            print(f"Has structured tables: {fields_info.get('has_structured_tables', False)}")
            
            print(f"\n=== TEXT FIELDS ===")
            for i, field in enumerate(fields_info['fields'], 1):
                print(f"{i:2d}. {field['name']} ({field['type']}) - {field['label']}")
            
            print(f"\n=== CHECKBOXES ===")
            for i, checkbox in enumerate(fields_info['checkboxes'], 1):
                print(f"{i:2d}. {checkbox['name']} - {checkbox['label']}")
            
            print(f"\n=== TABLES ===")
            for i, table in enumerate(fields_info['tables'], 1):
                print(f"{i:2d}. Table {table['index']} - Type: {table['table_type']}")
                print(f"    Has fields: {table['has_fields']}")
                print(f"    Fields in table: {len(table['fields'])}")
                print(f"    Headers: {table['headers']}")
                
                if table['fields']:
                    print("    Table fields:")
                    for field in table['fields']:
                        print(f"      - {field['name']} (row {field['row']}, cell {field['cell']}) - {field['type']}")
                print()
            
            print("✓ Enhanced template analysis completed successfully!")
            return True
            
    except Exception as e:
        print(f"✗ Error during enhanced analysis test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_analysis()
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
