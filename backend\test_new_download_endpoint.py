#!/usr/bin/env python3
"""
Test the new download endpoint for document generation.
"""

import sys
import os
import requests
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_download_endpoint():
    """Test the new /generate-download endpoint."""
    print("=== TESTING NEW DOWNLOAD ENDPOINT ===\n")
    
    # Backend URL
    base_url = "https://localhost:5000/api"
    
    # Test data
    test_data = {
        'customer_id': 1,  # Assuming customer ID 1 exists
        'form_data': {
            'bonnummer': '2024-TEST-001',
            'telefoon': '0612345678',
            'klantnummer': '12345',
            'contactpersoon': 'Test Gebruiker',
            'bedrijf': 'Test Bedrijf BV',
            'email': '<EMAIL>',
            'adres': 'Teststraat 123, 1234 AB Teststad',
            'type': 'Inbraakmeldsysteem',
            'inbraakmeldsysteem': True,
            'brandmeldsysteem': False,
            'cctv': False,
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False,
            'centrale_accu_opmerkingen': 'Accu spanning OK - 12.8V',
            'centrale_voeding_goed': False,
            'centrale_voeding_fout': True,
            'centrale_voeding_nvt': False,
            'centrale_voeding_opmerkingen': 'Voeding defect - moet vervangen worden',
            'klant_naam': 'Test Klant',
            'monteur_naam': 'Test Monteur',
            'datum': '11-07-2025',
            'begin_tijd': '09:00',
            'eind_tijd': '12:30',
            'klant_handtekening': '[KLANT HANDTEKENING]',
            'monteur_handtekening': '[MONTEUR HANDTEKENING]'
        }
    }
    
    # Test with template ID 1 (assuming it exists)
    template_id = 1
    
    try:
        print(f"Testing endpoint: {base_url}/document-templates/{template_id}/generate-download")
        print(f"Customer ID: {test_data['customer_id']}")
        print(f"Form data fields: {len(test_data['form_data'])}")
        
        # Make the request
        response = requests.post(
            f"{base_url}/document-templates/{template_id}/generate-download",
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            },
            verify=False,  # Skip SSL verification for localhost
            timeout=30
        )
        
        print(f"\nResponse status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ Request successful!")
            
            # Check content type
            content_type = response.headers.get('content-type', '')
            if 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' in content_type:
                print("✓ Correct content type returned")
            else:
                print(f"⚠ Unexpected content type: {content_type}")
            
            # Check content length
            content_length = len(response.content)
            print(f"✓ Document size: {content_length} bytes")
            
            if content_length > 0:
                # Save the document
                output_filename = f"test_endpoint_download_{template_id}.docx"
                with open(output_filename, 'wb') as f:
                    f.write(response.content)
                print(f"✓ Document saved to: {output_filename}")
                
                # Try to validate with python-docx
                try:
                    from docx import Document
                    doc = Document(output_filename)
                    print(f"✓ Document is valid DOCX: {len(doc.paragraphs)} paragraphs, {len(doc.tables)} tables")
                    
                    # Check if content was filled
                    first_table = doc.tables[0] if doc.tables else None
                    if first_table:
                        first_row = first_table.rows[0]
                        cell_text = first_row.cells[1].text if len(first_row.cells) > 1 else ""
                        if "2024-TEST-001" in cell_text:
                            print("✓ Template fields were filled correctly")
                        else:
                            print(f"⚠ Template fields may not be filled. Cell content: '{cell_text}'")
                    
                    return True
                    
                except Exception as e:
                    print(f"✗ Document validation failed: {e}")
                    return False
            else:
                print("✗ Document is empty")
                return False
                
        elif response.status_code == 401:
            print("✗ Authentication required")
            print("Note: This test requires authentication. Run from authenticated frontend.")
            return False
            
        elif response.status_code == 404:
            print("✗ Template or customer not found")
            print("Note: Make sure template ID 1 and customer ID 1 exist in the database.")
            return False
            
        else:
            print(f"✗ Request failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Response text: {response.text[:500]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Connection failed")
        print("Make sure the backend server is running on https://localhost:5000")
        return False
        
    except requests.exceptions.Timeout:
        print("✗ Request timed out")
        return False
        
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_service():
    """Test the service method directly."""
    print("\n=== TESTING SERVICE METHOD DIRECTLY ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Test data
        form_data = {
            'bonnummer': '2024-DIRECT-001',
            'telefoon': '0612345678',
            'klantnummer': '12345',
            'contactpersoon': 'Direct Test',
            'bedrijf': 'Direct Test BV',
            'email': '<EMAIL>',
            'adres': 'Directstraat 123, 1234 AB Directstad',
            'type': 'Inbraakmeldsysteem',
            'inbraakmeldsysteem': True,
            'brandmeldsysteem': False,
            'cctv': False,
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False,
            'centrale_accu_opmerkingen': 'Direct test - Accu OK',
            'klant_naam': 'Direct Klant',
            'monteur_naam': 'Direct Monteur',
            'datum': '11-07-2025'
        }
        
        # Test with template ID 1 and customer ID 1
        template_id = 1
        customer_id = 1
        
        print(f"Testing generate_document_content method...")
        print(f"Template ID: {template_id}, Customer ID: {customer_id}")
        
        # Generate document content
        document_content = service.generate_document_content(template_id, customer_id, form_data)
        
        print(f"✓ Document generated: {len(document_content)} bytes")
        
        # Save and validate
        output_filename = "test_direct_service.docx"
        with open(output_filename, 'wb') as f:
            f.write(document_content)
        print(f"✓ Document saved to: {output_filename}")
        
        # Validate with python-docx
        from docx import Document
        doc = Document(output_filename)
        print(f"✓ Document is valid: {len(doc.paragraphs)} paragraphs, {len(doc.tables)} tables")
        
        return True
        
    except Exception as e:
        print(f"✗ Direct service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Testing new download endpoint functionality...\n")
    
    # Test 1: Direct service method
    service_success = test_direct_service()
    
    # Test 2: HTTP endpoint
    endpoint_success = test_new_download_endpoint()
    
    if service_success and endpoint_success:
        print("\n🎉 All tests passed! The new download endpoint is working correctly.")
        sys.exit(0)
    elif service_success:
        print("\n⚠ Service method works, but endpoint test failed (likely authentication issue).")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
