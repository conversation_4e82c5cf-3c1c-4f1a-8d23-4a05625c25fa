#!/usr/bin/env python3
"""
Debug script to compare working vs non-working document generation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_document_generation():
    """Debug document generation step by step"""
    print("=== DEBUGGING DOCUMENT GENERATION ===\n")
    
    try:
        from app import create_app
        from app.services.document_template_service import DocumentTemplateService
        
        app, _ = create_app()
        
        with app.app_context():
            service = DocumentTemplateService()

            # Create test customer if not exists
            from app.repositories.customer_repository import CustomerRepository
            customer_repo = CustomerRepository()

            try:
                customer = customer_repo.get_by_id(1)
                if not customer:
                    print("Creating test customer...")
                    from app.models.customer import Customer
                    from app import db

                    customer = Customer(
                        name="Test Customer",
                        email="<EMAIL>",
                        phone="0612345678",
                        address="Test Street 123",
                        city="Test City",
                        postal_code="1234AB"
                    )
                    db.session.add(customer)
                    db.session.commit()
                    print(f"✓ Created test customer with ID: {customer.id}")
                    customer_id = customer.id
                else:
                    customer_id = customer.id
                    print(f"✓ Using existing customer ID: {customer_id}")
            except Exception as e:
                print(f"Customer setup error: {e}")
                customer_id = 1  # fallback

            # Test data
            template_id = 18  # Onderhoudsbon template
            form_data = {
                'telefoon': '0612345678',
                'email': '<EMAIL>',
                'centrale_voeding_goed': True,
                'centrale_voeding_fout': False,
                'centrale_voeding_nvt': False,
                'klant_naam': 'Test Klant',
                'monteur_naam': 'Test Monteur',
                'klant_handtekening': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                'monteur_handtekening': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
            }
            
            print("1. Testing BOTH methods with SAME template data...")

            # Get the template and prepare data ONCE
            from app.repositories.customer_repository import CustomerRepository
            customer_repo = CustomerRepository()
            customer = customer_repo.get_by_id(customer_id)

            template_data = service._merge_customer_and_form_data(customer, form_data)
            template = service.template_repo.get_by_id(template_id)

            # Get template content from Firebase
            from firebase_admin import storage
            bucket = storage.bucket()
            blob = bucket.blob(template.file_path)
            template_content = blob.download_as_bytes()

            print("✓ Template data prepared")

            try:
                # Method 1: Direct generation (working)
                print("\n1.1 Testing direct generation...")
                working_bytes = service._fill_docx_template_safe(template_content, template_data)
                print(f"✓ Direct method generated {len(working_bytes)} bytes")

                # Save working version
                with open('debug_working.docx', 'wb') as f:
                    f.write(working_bytes)
                print("✓ Saved working version to debug_working.docx")

                # Method 2: Save method generation
                print("\n1.2 Testing save method generation...")
                save_bytes = service._fill_docx_template_safe(template_content, template_data)
                print(f"✓ Save method generated {len(save_bytes)} bytes")

                # Save save version
                with open('debug_save.docx', 'wb') as f:
                    f.write(save_bytes)
                print("✓ Saved save version to debug_save.docx")

                # Compare the two generations
                print(f"\n1.3 COMPARING SAME-TIME GENERATIONS:")
                if working_bytes == save_bytes:
                    print("✓ SAME-TIME GENERATIONS ARE IDENTICAL!")
                else:
                    print("✗ SAME-TIME GENERATIONS ARE DIFFERENT!")
                    for i, (a, b) in enumerate(zip(working_bytes, save_bytes)):
                        if a != b:
                            print(f"First difference at byte {i}: method1={a} (0x{a:02x}), method2={b} (0x{b:02x})")
                            break

            except Exception as e:
                print(f"✗ Generation comparison failed: {e}")
                return False

            print("\n2. Testing FULL SAVE method (generate_document_from_template)...")
            try:
                saved_doc = service.generate_document_from_template(template_id, customer_id, form_data, 1)
                print(f"✓ Save method completed: {saved_doc}")
                
                # Now get the saved document from Firebase
                print("\n3. Testing Firebase retrieval...")
                from app.services.document_service import DocumentService
                doc_service = DocumentService()
                
                document = doc_service.get_document_by_id(saved_doc['id'])
                print(f"✓ Retrieved document: {document['name']}")
                
                # Download from Firebase URL
                import requests
                response = requests.get(document['file_url'])
                firebase_bytes = response.content
                print(f"✓ Downloaded from Firebase: {len(firebase_bytes)} bytes")
                
                # Save Firebase version
                with open('debug_firebase.docx', 'wb') as f:
                    f.write(firebase_bytes)
                print("✓ Saved Firebase version to debug_firebase.docx")
                
                # Compare bytes
                print(f"\n4. COMPARING BYTES:")
                print(f"Working bytes length: {len(working_bytes)}")
                print(f"Firebase bytes length: {len(firebase_bytes)}")

                if working_bytes == firebase_bytes:
                    print("✓ BYTES ARE IDENTICAL!")
                else:
                    print("✗ BYTES ARE DIFFERENT!")

                    # Find first difference
                    for i, (a, b) in enumerate(zip(working_bytes, firebase_bytes)):
                        if a != b:
                            print(f"First difference at byte {i}: working={a} (0x{a:02x}), firebase={b} (0x{b:02x})")
                            # Show context around the difference
                            start = max(0, i-10)
                            end = min(len(working_bytes), i+10)
                            print(f"Working context: {working_bytes[start:end]}")
                            print(f"Firebase context: {firebase_bytes[start:end]}")
                            break

                    # Check if one is shorter
                    if len(working_bytes) != len(firebase_bytes):
                        print(f"Length difference: working={len(working_bytes)}, firebase={len(firebase_bytes)}")

                    # Test if the issue is in the upload or download
                    print(f"\n4.1 TESTING UPLOAD PROCESS:")
                    try:
                        # Test direct upload of working bytes
                        import io
                        from app.utils.firebase import upload_file_to_storage

                        test_stream = io.BytesIO(working_bytes)
                        test_stream.name = "test_direct_upload.docx"

                        test_url, test_path = upload_file_to_storage(test_stream, "debug/test_direct_upload.docx")
                        print(f"✓ Direct upload completed: {test_path}")

                        # Download the test file
                        import requests
                        test_response = requests.get(test_url)
                        test_bytes = test_response.content

                        print(f"Test download length: {len(test_bytes)}")

                        if working_bytes == test_bytes:
                            print("✓ DIRECT UPLOAD/DOWNLOAD IS IDENTICAL - Problem is in document generation!")
                        else:
                            print("✗ DIRECT UPLOAD/DOWNLOAD IS DIFFERENT - Problem is in Firebase!")

                            # Find first difference in direct test
                            for i, (a, b) in enumerate(zip(working_bytes, test_bytes)):
                                if a != b:
                                    print(f"Direct test first difference at byte {i}: original={a}, firebase={b}")
                                    break

                    except Exception as e:
                        print(f"✗ Direct upload test failed: {e}")
                
                # Test if files can be opened
                print(f"\n5. TESTING FILE VALIDITY:")
                try:
                    from docx import Document
                    
                    # Test working file
                    doc1 = Document('debug_working.docx')
                    print(f"✓ Working file opens: {len(doc1.paragraphs)} paragraphs")
                    
                    # Test Firebase file
                    doc2 = Document('debug_firebase.docx')
                    print(f"✓ Firebase file opens: {len(doc2.paragraphs)} paragraphs")
                    
                except Exception as e:
                    print(f"✗ File validity test failed: {e}")
                
            except Exception as e:
                print(f"✗ Save method failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            return True
            
    except Exception as e:
        print(f"✗ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_document_generation()
    if success:
        print("\n🔍 DEBUG COMPLETED - Check debug_working.docx vs debug_firebase.docx")
    else:
        print("\n❌ DEBUG FAILED")
        sys.exit(1)
