#!/usr/bin/env python3
"""
Simple test to compare Firebase download methods.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
import requests
from firebase_admin import storage

def test_firebase_download():
    """Test Firebase download issue."""
    
    app, _ = create_app()
    
    with app.app_context():
        print("🧪 Testing Firebase download issue...")
        
        # Create a test file and upload it
        test_content = b"PK\x03\x04\x14\x00\x00\x00\x08\x00" + b"A" * 1000  # Mock DOCX header + content
        
        try:
            # Upload test file
            bucket = storage.bucket()
            test_path = "test/debug_download_test.docx"
            blob = bucket.blob(test_path)
            
            # Upload with correct content type
            blob.upload_from_string(
                test_content, 
                content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            )
            print(f"✅ Uploaded test file: {len(test_content)} bytes")
            
            # Generate signed URL
            signed_url = blob.generate_signed_url(
                version="v4",
                expiration=3600,
                method="GET"
            )
            print(f"🔗 Generated signed URL")
            
            # Method 1: Direct Firebase download
            firebase_content = blob.download_as_bytes()
            print(f"✅ Firebase direct: {len(firebase_content)} bytes")
            
            # Method 2: requests.get (current problematic method)
            response = requests.get(signed_url)
            response.raise_for_status()
            requests_content = response.content
            print(f"✅ requests.get: {len(requests_content)} bytes")
            print(f"📋 Response headers: {dict(response.headers)}")
            
            # Compare
            if firebase_content == requests_content:
                print("✅ Content is IDENTICAL - no Firebase download issue")
            else:
                print("❌ Content is DIFFERENT - Firebase download issue found!")
                print(f"📊 Firebase: {len(firebase_content)} bytes")
                print(f"📊 requests: {len(requests_content)} bytes")
                print(f"🔍 Firebase first 20 bytes: {firebase_content[:20]}")
                print(f"🔍 requests first 20 bytes: {requests_content[:20]}")
            
            # Clean up
            blob.delete()
            print("🧹 Cleaned up test file")
            
            return firebase_content == requests_content
            
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_firebase_download()
    if success:
        print("\n🎉 No Firebase download issue found!")
    else:
        print("\n❌ Firebase download issue confirmed!")
