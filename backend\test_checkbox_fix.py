#!/usr/bin/env python3
"""
Test script to verify checkbox field detection and processing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_checkbox_processing():
    """Test checkbox field detection and form data processing."""
    print("=== Testing Checkbox Processing ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Load the existing template
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        print(f"Loading template: {template_path}")
        
        # Read the template file
        with open(template_path, 'rb') as f:
            docx_content = f.read()
        
        print("✓ Template loaded successfully")
        
        # Test the enhanced field extraction
        print("\nTesting enhanced field extraction...")
        fields_info = service._extract_fields_from_docx(docx_content)
        
        print(f"\n=== CHECKBOX FIELD ANALYSIS ===")
        
        # Find checkbox-related fields
        checkbox_fields = []
        text_fields = []
        
        for field in fields_info['fields']:
            field_name = field['name'].lower()
            if any(keyword in field_name for keyword in ['goed', 'fout', 'nvt']):
                checkbox_fields.append(field)
            else:
                text_fields.append(field)
        
        for field in fields_info['checkboxes']:
            field_name = field['name'].lower()
            if any(keyword in field_name for keyword in ['goed', 'fout', 'nvt']):
                checkbox_fields.append(field)
            else:
                text_fields.append(field)
        
        print(f"Checkbox-related fields found: {len(checkbox_fields)}")
        for i, field in enumerate(checkbox_fields[:10], 1):
            print(f"  {i:2d}. {field['name']} ({field['type']}) - {field['label']}")
        
        if len(checkbox_fields) > 10:
            print(f"      ... and {len(checkbox_fields) - 10} more")
        
        # Test form data processing
        print(f"\n=== TESTING FORM DATA PROCESSING ===")
        
        # Create sample form data like what would come from the frontend
        sample_form_data = {
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False,
            'centrale_accu_opmerkingen': 'Accu is in goede staat',
            'centrale_voeding_goed': False,
            'centrale_voeding_fout': True,
            'centrale_voeding_nvt': False,
            'centrale_voeding_opmerkingen': 'Voeding moet vervangen worden',
            'bonnummer': '12345',
            'bedrijf': 'Test Bedrijf BV',
            'klant_naam': 'Jan de Tester',
            'monteur_naam': 'Piet Monteur'
        }
        
        print("Sample form data:")
        for key, value in sample_form_data.items():
            print(f"  {key}: {value} ({type(value).__name__})")
        
        # Create a mock customer object
        class MockCustomer:
            def __init__(self):
                self.id = 123
                self.name = "Test Customer"
                self.phone = "0612345678"
                self.email = "<EMAIL>"
        
        customer = MockCustomer()
        
        # Test the merge function
        merged_data = service._merge_customer_and_form_data(customer, sample_form_data)
        
        print(f"\n=== MERGED DATA ANALYSIS ===")
        print(f"Total fields in merged data: {len(merged_data)}")
        
        # Check boolean fields
        boolean_fields = {k: v for k, v in merged_data.items() if isinstance(v, bool)}
        print(f"\nBoolean fields: {len(boolean_fields)}")
        for key, value in boolean_fields.items():
            print(f"  {key}: {value}")
        
        # Check text fields
        text_fields = {k: v for k, v in merged_data.items() if isinstance(v, str) and v}
        print(f"\nText fields: {len(text_fields)}")
        for key, value in list(text_fields.items())[:10]:
            print(f"  {key}: {value}")
        if len(text_fields) > 10:
            print(f"  ... and {len(text_fields) - 10} more")
        
        # Test conditional field processing
        print(f"\n=== TESTING CONDITIONAL FIELD PROCESSING ===")
        
        # Test the replace template fields function
        sample_xml = """
        <w:t>{bonnummer}</w:t>
        <w:t>{#centrale_accu_goed}✓{/centrale_accu_goed}</w:t>
        <w:t>{^centrale_accu_goed} {/centrale_accu_goed}</w:t>
        <w:t>{centrale_accu_opmerkingen}</w:t>
        """
        
        processed_xml = service._replace_template_fields(sample_xml, merged_data)
        
        print("Original XML:")
        print(sample_xml)
        print("\nProcessed XML:")
        print(processed_xml)
        
        # Verify that checkboxes work correctly
        if '✓' in processed_xml and 'Test Bedrijf BV' in processed_xml:
            print("\n✓ Conditional fields processed correctly!")
        else:
            print("\n✗ Conditional field processing may have issues")
        
        print("\n✓ Checkbox processing test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error during checkbox processing test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_checkbox_processing()
    if success:
        print("\n🎉 Checkbox processing test completed!")
        sys.exit(0)
    else:
        print("\n❌ Checkbox processing test failed!")
        sys.exit(1)
