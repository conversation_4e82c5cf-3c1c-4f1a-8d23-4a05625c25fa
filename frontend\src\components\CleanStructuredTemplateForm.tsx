import React, { useState } from 'react';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import api from '../api';
import SignaturePad from './SignaturePad';

interface CleanStructuredTemplateFormProps {
  templateId: number;
  templateName: string;
  customerId: number;
  onSave: (blob: Blob | null, fileName: string, document?: any) => void;
  onCancel: () => void;
}

const CleanStructuredTemplateForm: React.FC<CleanStructuredTemplateFormProps> = ({
  templateId,
  templateName,
  customerId,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Onderhoudsbon specific fields (removed auto-filled fields)
  const basicFields = [
    { name: 'telefoon', label: 'Telefoonnummer', type: 'tel' },
    { name: 'email', label: 'Email', type: 'email' },
    { name: 'adres', label: 'Adres', type: 'text' },
    { name: 'type', label: 'Type', type: 'text' }
  ];

  const systemTypes = [
    { name: 'inbraakmeldsysteem', label: 'Inbraakmeldsysteem' },
    { name: 'brandmeldsysteem', label: 'Brandmeldsysteem' },
    { name: 'cctv', label: 'CCTV' }
  ];

  const centraleComponents = [
    { name: 'centrale_accu', label: 'Accu' },
    { name: 'centrale_voeding', label: 'Voeding' },
    { name: 'centrale_lusspanning', label: 'Lusspanning' },
    { name: 'centrale_uitlezing', label: 'Uitlezing' },
    { name: 'centrale_algemeen', label: 'Algemene werking' }
  ];

  const detectieComponents = [
    { name: 'detectie_bevestiging', label: 'Bevestiging' },
    { name: 'detectie_werking', label: 'Werking' },
    { name: 'detectie_projectie', label: 'Projectie' },
    { name: 'detectie_algemeen', label: 'Detectie algemeen' }
  ];

  const bekabelingComponents = [
    { name: 'bekabeling_bevestiging', label: 'Bevestiging' },
    { name: 'bekabeling_afscherming', label: 'Afscherming' }
  ];

  const signaleringComponents = [
    { name: 'signalering_bevestiging', label: 'Bevestiging' },
    { name: 'signalering_werking_flits', label: 'Werking flits' },
    { name: 'signalering_werking_sirene', label: 'Werking sirene' },
    { name: 'signalering_algemeen', label: 'Signalering algemeen' }
  ];

  const doormeldingComponents = [
    { name: 'doormelding_schakel', label: 'Schakelmelding' },
    { name: 'doormelding_inbraak', label: 'Inbraak' },
    { name: 'doormelding_overval', label: 'Overval' },
    { name: 'doormelding_brand', label: 'Brand' },
    { name: 'doormelding_technisch', label: 'Technisch' },
    { name: 'doormelding_contact', label: 'Contact gewenst MK' }
  ];

  const signatureFields = [
    { name: 'klant_naam', label: 'Klant naam', type: 'text' },
    { name: 'monteur_naam', label: 'Monteur naam', type: 'text' },
    { name: 'datum', label: 'Datum', type: 'date' },
    { name: 'begin_tijd', label: 'Begin tijd', type: 'time' },
    { name: 'eind_tijd', label: 'Eind tijd', type: 'time' }
  ];

  const specialFields = [
    { name: 'installatie_in_orde', label: 'Installatie in orde', type: 'checkbox' },
    { name: 'installatie_niet_in_orde', label: 'Installatie niet in orde', type: 'checkbox' },
    { name: 'ja_opmerking', label: 'Ja, opmerking', type: 'textarea' },
    { name: 'nee_onbekend', label: 'Nee / onbekend', type: 'textarea' }
  ];

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleRadioChange = (componentName: string, selection: 'goed' | 'fout' | 'nvt') => {
    setFormData(prev => ({
      ...prev,
      [`${componentName}_goed`]: selection === 'goed',
      [`${componentName}_fout`]: selection === 'fout',
      [`${componentName}_nvt`]: selection === 'nvt'
    }));
  };

  const getRadioSelection = (componentName: string): 'goed' | 'fout' | 'nvt' | '' => {
    if (formData[`${componentName}_goed`]) return 'goed';
    if (formData[`${componentName}_fout`]) return 'fout';
    if (formData[`${componentName}_nvt`]) return 'nvt';
    return '';
  };

  const renderComponentTable = (title: string, components: Array<{name: string, label: string}>) => (
    <div>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <div className="overflow-x-auto">
        <table className="table table-bordered w-full">
          <thead>
            <tr>
              <th>Component</th>
              <th>Goed</th>
              <th>Fout</th>
              <th>NVT</th>
              <th>Opmerkingen</th>
            </tr>
          </thead>
          <tbody>
            {components.map(component => (
              <tr key={component.name}>
                <td className="font-medium">{component.label}</td>
                <td className="text-center">
                  <input
                    type="radio"
                    name={`${component.name}_selection`}
                    className="radio radio-success"
                    checked={getRadioSelection(component.name) === 'goed'}
                    onChange={() => handleRadioChange(component.name, 'goed')}
                    disabled={generating}
                  />
                </td>
                <td className="text-center">
                  <input
                    type="radio"
                    name={`${component.name}_selection`}
                    className="radio radio-error"
                    checked={getRadioSelection(component.name) === 'fout'}
                    onChange={() => handleRadioChange(component.name, 'fout')}
                    disabled={generating}
                  />
                </td>
                <td className="text-center">
                  <input
                    type="radio"
                    name={`${component.name}_selection`}
                    className="radio radio-warning"
                    checked={getRadioSelection(component.name) === 'nvt'}
                    onChange={() => handleRadioChange(component.name, 'nvt')}
                    disabled={generating}
                  />
                </td>
                <td>
                  <textarea
                    className="textarea textarea-bordered w-full text-sm"
                    value={formData[`${component.name}_opmerkingen`] || ''}
                    onChange={(e) => handleFieldChange(`${component.name}_opmerkingen`, e.target.value)}
                    disabled={generating}
                    placeholder="Opmerkingen..."
                    rows={2}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const handleSave = async () => {
    try {
      setGenerating(true);
      setError(null);

      console.log('Saving with form data:', formData);

      // Use the original generate endpoint (we'll fix the backend to use the working method)
      const savedDocument = await api.post(`/document-templates/${templateId}/generate`, {
        customer_id: customerId,
        form_data: formData
      });

      console.log('Document saved successfully:', savedDocument.data);
      onSave(null, savedDocument.data.name, savedDocument.data);
    } catch (err: any) {
      console.error('Save error:', err);
      setError(`Failed to save document: ${err.response?.data?.error || err.message}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async () => {
    try {
      setGenerating(true);
      setError(null);

      console.log('Downloading with form data:', formData);

      // Use the backend API to generate and download the document
      const response = await api.post(`/document-templates/${templateId}/generate-download`, {
        customer_id: customerId,
        form_data: formData
      }, {
        responseType: 'blob'
      });

      // Create blob and download
      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });

      // Generate filename (same format as save)
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${templateName.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Download the file
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('Download completed');
    } catch (err: any) {
      console.error('Download error:', err);
      setError(`Failed to download document: ${err.response?.data?.error || err.message}`);
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-amspm-text">
          {templateName}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={handleDownload}
            disabled={generating}
            className="btn btn-outline btn-sm flex items-center"
          >
            {generating ? <FaSpinner className="animate-spin mr-2" /> : <FaDownload className="mr-2" />}
            Download
          </button>
          <button
            onClick={handleSave}
            disabled={generating}
            className="btn btn-primary btn-sm flex items-center"
          >
            {generating ? <FaSpinner className="animate-spin mr-2" /> : <FaSave className="mr-2" />}
            Save
          </button>
          <button
            onClick={onCancel}
            disabled={generating}
            className="btn btn-outline btn-sm"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="alert alert-error mb-4">
          <span>{error}</span>
        </div>
      )}

      <div className="space-y-8">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium mb-4">📋 Basis Informatie</h3>
          <div className="grid grid-cols-2 gap-4">
            {basicFields.map(field => (
              <div key={field.name}>
                <label className="block text-sm font-medium mb-1">{field.label}</label>
                <input
                  type={field.type}
                  className="input input-bordered w-full"
                  value={formData[field.name] || ''}
                  onChange={(e) => handleFieldChange(field.name, e.target.value)}
                  disabled={generating}
                />
              </div>
            ))}
          </div>
        </div>

        {/* System Types */}
        <div>
          <h3 className="text-lg font-medium mb-4">🔧 Type Installatie</h3>
          <div className="flex space-x-6">
            {systemTypes.map(system => (
              <label key={system.name} className="flex items-center">
                <input
                  type="checkbox"
                  className="checkbox checkbox-primary mr-2"
                  checked={formData[system.name] || false}
                  onChange={(e) => handleFieldChange(system.name, e.target.checked)}
                  disabled={generating}
                />
                {system.label}
              </label>
            ))}
          </div>
        </div>

        {/* All Component Tables */}
        {renderComponentTable('⚡ Centrale / kiezer', centraleComponents)}
        {renderComponentTable('🔍 Detectie', detectieComponents)}
        {renderComponentTable('🔌 Bekabeling', bekabelingComponents)}
        {renderComponentTable('🚨 Signalering', signaleringComponents)}
        {renderComponentTable('📡 Doormelding', doormeldingComponents)}

        {/* Special Fields */}
        <div>
          <h3 className="text-lg font-medium mb-4">📋 Algemene staat</h3>
          <div className="space-y-4">
            <div className="flex space-x-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="checkbox checkbox-primary mr-2"
                  checked={formData['installatie_in_orde'] || false}
                  onChange={(e) => handleFieldChange('installatie_in_orde', e.target.checked)}
                  disabled={generating}
                />
                Installatie in orde
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="checkbox checkbox-primary mr-2"
                  checked={formData['installatie_niet_in_orde'] || false}
                  onChange={(e) => handleFieldChange('installatie_niet_in_orde', e.target.checked)}
                  disabled={generating}
                />
                Installatie niet in orde
              </label>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Ja, opmerking:</label>
                <textarea
                  className="textarea textarea-bordered w-full"
                  value={formData['ja_opmerking'] || ''}
                  onChange={(e) => handleFieldChange('ja_opmerking', e.target.value)}
                  disabled={generating}
                  placeholder="Opmerking..."
                  rows={3}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Nee / onbekend:</label>
                <textarea
                  className="textarea textarea-bordered w-full"
                  value={formData['nee_onbekend'] || ''}
                  onChange={(e) => handleFieldChange('nee_onbekend', e.target.value)}
                  disabled={generating}
                  placeholder="Nee / onbekend..."
                  rows={3}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Signatures */}
        <div>
          <h3 className="text-lg font-medium mb-4">✍️ Akkoord</h3>
          <div className="grid grid-cols-2 gap-6">
            {/* Customer Side */}
            <div className="border rounded-lg p-4">
              <h4 className="font-medium mb-4">Akkoord opdrachtgever</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Naam:</label>
                  <input
                    type="text"
                    className="input input-bordered w-full"
                    value={formData['klant_naam'] || ''}
                    onChange={(e) => handleFieldChange('klant_naam', e.target.value)}
                    disabled={generating}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Datum:</label>
                  <input
                    type="date"
                    className="input input-bordered w-full"
                    value={formData['datum'] || ''}
                    onChange={(e) => handleFieldChange('datum', e.target.value)}
                    disabled={generating}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Handtekening:</label>
                  <SignaturePad
                    value={formData['klant_handtekening'] || ''}
                    onChange={(signature) => handleFieldChange('klant_handtekening', signature)}
                    disabled={generating}
                    label=""
                  />
                </div>
              </div>
            </div>

            {/* Technician Side */}
            <div className="border rounded-lg p-4">
              <h4 className="font-medium mb-4">Monteur</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Naam:</label>
                  <input
                    type="text"
                    className="input input-bordered w-full"
                    value={formData['monteur_naam'] || ''}
                    onChange={(e) => handleFieldChange('monteur_naam', e.target.value)}
                    disabled={generating}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Begin/eindtijd:</label>
                  <div className="flex space-x-2">
                    <input
                      type="time"
                      className="input input-bordered flex-1"
                      value={formData['begin_tijd'] || ''}
                      onChange={(e) => handleFieldChange('begin_tijd', e.target.value)}
                      disabled={generating}
                    />
                    <span className="self-center">-</span>
                    <input
                      type="time"
                      className="input input-bordered flex-1"
                      value={formData['eind_tijd'] || ''}
                      onChange={(e) => handleFieldChange('eind_tijd', e.target.value)}
                      disabled={generating}
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Handtekening:</label>
                  <SignaturePad
                    value={formData['monteur_handtekening'] || ''}
                    onChange={(signature) => handleFieldChange('monteur_handtekening', signature)}
                    disabled={generating}
                    label=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CleanStructuredTemplateForm;
