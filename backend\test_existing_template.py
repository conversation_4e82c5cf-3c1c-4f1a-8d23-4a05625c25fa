#!/usr/bin/env python3
"""
Test script to analyze the existing onderhoudsbon template with enhanced analysis.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_existing_template():
    """Test the enhanced template analysis with the existing onderhoudsbon template."""
    print("=== Testing Existing Onderhoudsbon Template ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        from app import create_app
        
        # Create Flask app context
        app = create_app()

        with app.app_context():
            service = DocumentTemplateService()
            
            # Load the existing template
            template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
            
            if not os.path.exists(template_path):
                print(f"✗ Template file not found: {template_path}")
                return False
            
            print(f"Loading template: {template_path}")
            
            # Read the template file
            with open(template_path, 'rb') as f:
                docx_content = f.read()
            
            print("✓ Template loaded successfully")
            
            # Test the enhanced field extraction
            print("\nTesting enhanced field extraction...")
            fields_info = service._extract_fields_from_docx(docx_content)
            
            print(f"\n=== ANALYSIS RESULTS ===")
            print(f"Fields found: {len(fields_info['fields'])}")
            print(f"Checkboxes found: {len(fields_info['checkboxes'])}")
            print(f"Tables found: {len(fields_info['tables'])}")
            print(f"Has structured tables: {fields_info.get('has_structured_tables', False)}")
            
            print(f"\n=== TEXT FIELDS ===")
            for i, field in enumerate(fields_info['fields'], 1):
                table_info = ""
                if field.get('in_table'):
                    table_info = f" [Table {field.get('table_index', '?')} - {field.get('table_type', 'unknown')}]"
                print(f"{i:2d}. {field['name']} ({field['type']}) - {field['label']}{table_info}")
            
            print(f"\n=== CHECKBOXES ===")
            for i, checkbox in enumerate(fields_info['checkboxes'], 1):
                table_info = ""
                if checkbox.get('in_table'):
                    table_info = f" [Table {checkbox.get('table_index', '?')} - {checkbox.get('table_type', 'unknown')}]"
                print(f"{i:2d}. {checkbox['name']} - {checkbox['label']}{table_info}")
            
            print(f"\n=== TABLES DETAILED ANALYSIS ===")
            for i, table in enumerate(fields_info['tables'], 1):
                print(f"\n{i:2d}. Table {table['index']} - Type: {table['table_type']}")
                print(f"    Has fields: {table['has_fields']}")
                print(f"    Fields in table: {len(table['fields'])}")
                print(f"    Headers: {table['headers']}")
                
                if table.get('structure'):
                    print(f"    Rows: {len(table['structure'])}")
                    for row_idx, row in enumerate(table['structure'][:3]):  # Show first 3 rows
                        print(f"      Row {row_idx}: {len(row['cells'])} cells, {len(row['fields'])} fields, header: {row['is_header']}")
                
                if table['fields']:
                    print("    Table fields:")
                    for field in table['fields'][:5]:  # Show first 5 fields
                        print(f"      - {field['name']} (row {field['row']}, cell {field['cell']}) - {field['type']}")
                    if len(table['fields']) > 5:
                        print(f"      ... and {len(table['fields']) - 5} more fields")
            
            # Determine if this template should use structured form
            should_use_structured = fields_info.get('has_structured_tables', False) or \
                any(table['table_type'] in ['checkbox_table', 'signature_table', 'form_table'] for table in fields_info['tables'])
            
            print(f"\n=== RECOMMENDATION ===")
            print(f"Should use structured form: {should_use_structured}")
            
            if should_use_structured:
                print("✓ This template has proper table structure and will use the new structured form display")
            else:
                print("ℹ This template will use the traditional field-by-field display")
                print("  Consider updating the template to use proper table structures for better display")
            
            print("\n✓ Enhanced template analysis completed successfully!")
            return True
            
    except Exception as e:
        print(f"✗ Error during template analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_existing_template()
    if success:
        print("\n🎉 Analysis completed!")
        sys.exit(0)
    else:
        print("\n❌ Analysis failed!")
        sys.exit(1)
