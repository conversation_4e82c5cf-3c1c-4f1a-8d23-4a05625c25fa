#!/usr/bin/env python3
"""
Test the backend endpoint directly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_backend_endpoint():
    """Test the backend generate_document_content method."""
    print("=== TESTING BACKEND ENDPOINT METHOD ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        from docx import Document
        import io
        
        service = DocumentTemplateService()
        
        # Test data (minimal to avoid issues)
        form_data = {
            'bonnummer': '2024-BACKEND-001',
            'telefoon': '0612345678',
            'bedrijf': 'Backend Test BV',
            'contactpersoon': 'Backend Tester',
            'email': '<EMAIL>',
            'adres': 'Backendstraat 123',
            'type': 'Test systeem',
            'inbraakmeldsysteem': True,
            'brandmeldsysteem': False,
            'cctv': False,
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False,
            'centrale_accu_opmerkingen': 'Backend test - alles OK',
            'klant_naam': 'Backend Klant',
            'monteur_naam': 'Backend Monteur',
            'datum': '11-07-2025',
            'begin_tijd': '09:00',
            'eind_tijd': '17:00'
        }
        
        print(f"Form data prepared: {len(form_data)} fields")
        
        # Use template ID 1 and customer ID 1 (assuming they exist)
        template_id = 1
        customer_id = 1
        
        print(f"Testing generate_document_content...")
        print(f"Template ID: {template_id}")
        print(f"Customer ID: {customer_id}")
        
        # Generate document content
        document_content = service.generate_document_content(template_id, customer_id, form_data)
        
        print(f"✓ Document generated successfully!")
        print(f"  Size: {len(document_content)} bytes")
        
        # Validate the document
        doc = Document(io.BytesIO(document_content))
        print(f"✓ Document is valid DOCX")
        print(f"  Paragraphs: {len(doc.paragraphs)}")
        print(f"  Tables: {len(doc.tables)}")
        
        # Save the document
        output_filename = "test_backend_endpoint_result.docx"
        with open(output_filename, 'wb') as f:
            f.write(document_content)
        print(f"✓ Document saved to: {output_filename}")
        
        # Check if content was filled
        if doc.tables:
            first_table = doc.tables[0]
            if len(first_table.rows) > 0 and len(first_table.rows[0].cells) > 1:
                cell_content = first_table.rows[0].cells[1].text
                if "2024-BACKEND-001" in cell_content:
                    print(f"✓ Template fields were filled correctly!")
                    print(f"  Found: {cell_content}")
                else:
                    print(f"⚠ Template fields may not be filled")
                    print(f"  Cell content: '{cell_content}'")
        
        # Try to open the document
        try:
            import subprocess
            print(f"\nTrying to open the document...")
            subprocess.run(['start', output_filename], shell=True, check=False)
            print(f"✓ Document opened (check if it works in Word)")
        except:
            print(f"⚠ Could not auto-open document")
        
        return True
        
    except Exception as e:
        print(f"✗ Backend endpoint test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_backend_endpoint()
    if success:
        print("\n🎉 Backend endpoint test completed!")
        print("\nIf the document opens correctly in Word, then the backend is working.")
        print("The issue is in the frontend not using the backend API.")
        sys.exit(0)
    else:
        print("\n❌ Backend endpoint test failed!")
        sys.exit(1)
