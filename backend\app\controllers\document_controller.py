# app/routes/document_routes.py
from flask import Blueprint, request, jsonify, current_app
from app.services.document_service import DocumentService

from app.utils.security import role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.cache_decorators import cached_list, cached_detail
from app.utils.file_validation import validate_file
from app.schemas.document_schema import document_schema, documents_schema, document_upload_schema
import logging
import datetime as dt
from marshmallow import ValidationError

document_bp = Blueprint("document", __name__)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

document_service = DocumentService()


@document_bp.route("/customer/<int:customer_id>", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
@cached_detail(entity_type="customer_documents", timeout=300)  # Cache for 5 minutes
def get_documents_by_customer(customer_id):
    try:
        documents = document_service.get_documents_by_customer(customer_id)

        # All authenticated users can view documents for customers with assigned events
        current_user = request.current_user

        logger.info(f"Fetched {len(documents)} documents for customer {customer_id}")
        return jsonify(documents), 200
    except Exception as e:
        logger.error(f"Failed to fetch documents for customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_bp.route('/upcoming-expirations', methods=['GET'])
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="document_expirations", timeout=300)  # Cache for 5 minutes
def get_upcoming_expirations():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        documents, total = document_service.get_upcoming_expirations(page=page, per_page=per_page)

        return jsonify({
            'documents': [doc.to_dict() for doc in documents],
            'total': total,
            'page': page,
            'per_page': per_page,
            'message': 'Upcoming expirations retrieved successfully'
        }), 200
    except Exception as e:
        logger.error(f"Error in get_upcoming_expirations: {str(e)}")
        return jsonify({'error': str(e)}), 500

@document_bp.route('/expired-documents', methods=['GET'])
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="expired_documents", timeout=300)  # Cache for 5 minutes
def get_expired_documents():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        documents, total = document_service.get_expired_documents(page=page, per_page=per_page)

        return jsonify({
            'documents': [doc.to_dict() for doc in documents],
            'total': total,
            'page': page,
            'per_page': per_page,
            'message': 'Expired documents retrieved successfully'
        }), 200
    except Exception as e:
        logger.error(f"Error in get_expired_documents: {str(e)}")
        return jsonify({'error': str(e)}), 500

@document_bp.route("/<int:document_id>", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
@cached_detail(entity_type="document", timeout=300)  # Cache for 5 minutes
def get_document(document_id):
    try:
        document = document_service.get_document_by_id(document_id)

        # All authenticated users can view documents
        current_user = request.current_user

        return jsonify(document), 200
    except Exception as e:
        logger.error(f"Failed to fetch document {document_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@document_bp.route("", methods=["POST"])
@roles_required("monteur", "verkoper", "administrator")
@rate_limit("60/minute")
def create_document():
    try:
        # Validate file
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]

        # Validate file using the file validation utility
        is_valid, message = validate_file(file)
        if not is_valid:
            logger.warning(f"File validation failed: {message}")
            return jsonify({"error": message}), 400

        # Parse and validate form data
        form_data = {
            "customer_id": request.form.get("customer_id"),
            "event_id": request.form.get("event_id"),
            "document_type": request.form.get("document_type"),
            "related_document_id": request.form.get("related_document_id"),
            "expiry_date": request.form.get("expiry_date"),
            "document_not_applicable": request.form.get("document_not_applicable") == "true",
            "use_version_status": request.form.get("use_version_status", "true") == "true",
            "version_status": request.form.get("version_status", "active")
        }

        # Convert string values to appropriate types
        if form_data["customer_id"]:
            form_data["customer_id"] = int(form_data["customer_id"])

        if form_data["event_id"]:
            form_data["event_id"] = int(form_data["event_id"])

        if form_data["related_document_id"]:
            form_data["related_document_id"] = int(form_data["related_document_id"])

        # Validate form data using the schema
        errors = document_upload_schema.validate(form_data)
        if errors:
            logger.warning(f"Document upload validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        current_user = request.current_user
        uploaded_by = current_user.id

        # All authenticated users can upload documents

        # Only administrators can upload documents without an event
        if current_user.role != "administrator" and not form_data["event_id"]:
            return jsonify({"error": "Non-administrators must upload documents through an event"}), 403

        # Create the document
        document = document_service.create_document(
            form_data["customer_id"],
            form_data["event_id"],
            file,
            form_data["document_type"],
            uploaded_by,
            form_data["expiry_date"],
            form_data["related_document_id"],
            form_data.get("use_version_status", True),
            form_data.get("version_status", "active")
        )

        logger.info(f"Document created successfully: {document['id']}")
        return jsonify(document), 201
    except ValidationError as e:
        logger.warning(f"Document upload validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except ValueError as ve:
        logger.warning(f"Invalid value in document upload: {str(ve)}")
        return jsonify({"error": str(ve)}), 400
    except Exception as e:
        logger.error(f"Error creating document: {str(e)}")
        return jsonify({"error": str(e)}), 400



@document_bp.route("/customer/<int:customer_id>", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def create_document_for_customer(customer_id):
    try:
        # Validate file
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]

        # Validate file using the file validation utility
        is_valid, message = validate_file(file)
        if not is_valid:
            logger.warning(f"File validation failed: {message}")
            return jsonify({"error": message}), 400

        # Parse and validate form data
        form_data = {
            "customer_id": customer_id,
            "event_id": None,
            "document_type": request.form.get("document_type"),
            "related_document_id": request.form.get("related_document_id"),
            "expiry_date": request.form.get("expiry_date"),
            "document_not_applicable": request.form.get("document_not_applicable") == "true",
            "use_version_status": request.form.get("use_version_status", "true") == "true",
            "version_status": request.form.get("version_status", "active")
        }

        # Convert string values to appropriate types
        if form_data["related_document_id"]:
            form_data["related_document_id"] = int(form_data["related_document_id"])

        # Validate form data using the schema
        errors = document_upload_schema.validate(form_data)
        if errors:
            logger.warning(f"Document upload validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        current_user = request.current_user
        uploaded_by = current_user.id

        # This endpoint is only for administrators, and they can upload any document type without an event
        # No additional checks needed here

        # Create the document
        document = document_service.create_document(
            customer_id,
            None,
            file,
            form_data["document_type"],
            uploaded_by,
            form_data["expiry_date"],
            form_data["related_document_id"],
            form_data.get("use_version_status", True),
            form_data.get("version_status", "active")
        )

        logger.info(f"Document created successfully for customer {customer_id}: {document['id']}")
        return jsonify(document), 201
    except ValidationError as e:
        logger.warning(f"Document upload validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except ValueError as ve:
        logger.warning(f"Invalid value in document upload: {str(ve)}")
        return jsonify({"error": str(ve)}), 400
    except Exception as e:
        logger.error(f"Error creating document for customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 400

@document_bp.route("/<int:document_id>/file", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
def get_document_file(document_id):
    """
    Serve the document file by ID.

    This endpoint retrieves the document by ID and serves the file content directly.
    """
    try:
        from flask import Response
        from firebase_admin import storage

        # Get the document
        document = document_service.get_document_by_id(document_id)

        # All authenticated users can view documents
        current_user = request.current_user

        # Get the file directly from Firebase Storage using the storage path
        bucket = storage.bucket()
        blob = bucket.blob(document["file_path"])

        if not blob.exists():
            return jsonify({"error": "File not found in storage"}), 404

        # Download the file content directly from Firebase
        file_content = blob.download_as_bytes()

        # Determine content type based on file extension
        import os
        file_ext = os.path.splitext(document["name"])[1].lower()

        if file_ext == '.docx':
            content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif file_ext == '.pdf':
            content_type = 'application/pdf'
        else:
            content_type = 'application/octet-stream'

        # Return the file content directly
        return Response(
            file_content,
            mimetype=content_type,
            headers={
                'Content-Disposition': f'attachment; filename="{document["name"]}"',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )
    except Exception as e:
        logger.error(f"Error serving document file {document_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500



@document_bp.route("/<int:document_id>", methods=["DELETE"])
@role_required("administrator")
@rate_limit("60/minute")
def delete_document(document_id):
    try:
        document_service.delete_document(document_id)
        logger.info(f"Deleted document {document_id}")
        return jsonify({"message": "Document deleted"}), 200
    except Exception as e:
        error_message = str(e)
        logger.error(f"Failed to delete document {document_id}: {error_message}")

        # Return appropriate status code based on error type
        if "not found" in error_message.lower():
            return jsonify({"error": error_message, "document_id": document_id}), 404
        else:
            return jsonify({"error": error_message, "document_id": document_id}), 500
