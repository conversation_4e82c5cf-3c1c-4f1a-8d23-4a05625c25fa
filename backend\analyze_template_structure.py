#!/usr/bin/env python3
"""
Analyze the actual structure of the onderhoudsbon template.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_template_structure():
    """Analyze the template structure to understand field placement."""
    print("=== Analyzing Template Structure ===\n")
    
    try:
        from docx import Document
        
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        print(f"Loading template: {template_path}")
        
        # Load the template using python-docx
        doc = Document(template_path)
        
        print("✓ Template loaded successfully")
        print(f"Document has {len(doc.paragraphs)} paragraphs and {len(doc.tables)} tables")
        
        # Analyze paragraphs
        print(f"\n=== PARAGRAPHS ===")
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text and ('{' in text or '}' in text):
                print(f"Paragraph {i}: {text}")
        
        # Analyze tables
        print(f"\n=== TABLES ===")
        for table_idx, table in enumerate(doc.tables):
            print(f"\nTable {table_idx}:")
            print(f"  Rows: {len(table.rows)}, Columns: {len(table.columns)}")
            
            # Show first few rows to understand structure
            for row_idx, row in enumerate(table.rows[:5]):  # Show first 5 rows
                row_texts = []
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_texts.append(f"[{cell_idx}]: {cell_text}")
                
                if row_texts:
                    print(f"    Row {row_idx}: {' | '.join(row_texts)}")
            
            if len(table.rows) > 5:
                print(f"    ... and {len(table.rows) - 5} more rows")
            
            # Look for template fields in this table
            fields_found = []
            for row in table.rows:
                for cell in row.cells:
                    cell_text = cell.text
                    if '{' in cell_text and '}' in cell_text:
                        import re
                        fields = re.findall(r'\{([^}]+)\}', cell_text)
                        for field in fields:
                            if field not in fields_found:
                                fields_found.append(field)
            
            if fields_found:
                print(f"    Template fields found: {fields_found}")
        
        # Look for all template fields in the entire document
        print(f"\n=== ALL TEMPLATE FIELDS ===")
        all_fields = set()
        
        # Check paragraphs
        for paragraph in doc.paragraphs:
            import re
            fields = re.findall(r'\{([^}]+)\}', paragraph.text)
            all_fields.update(fields)
        
        # Check tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    import re
                    fields = re.findall(r'\{([^}]+)\}', cell.text)
                    all_fields.update(fields)
        
        # Check headers and footers
        for section in doc.sections:
            if section.header:
                for paragraph in section.header.paragraphs:
                    import re
                    fields = re.findall(r'\{([^}]+)\}', paragraph.text)
                    all_fields.update(fields)
            
            if section.footer:
                for paragraph in section.footer.paragraphs:
                    import re
                    fields = re.findall(r'\{([^}]+)\}', paragraph.text)
                    all_fields.update(fields)
        
        print(f"Total unique template fields found: {len(all_fields)}")
        
        # Categorize fields
        simple_fields = []
        conditional_fields = []
        negative_fields = []
        
        for field in sorted(all_fields):
            if field.startswith('#'):
                conditional_fields.append(field)
            elif field.startswith('^'):
                negative_fields.append(field)
            elif field.startswith('/'):
                pass  # Closing tags
            else:
                simple_fields.append(field)
        
        print(f"\nSimple fields ({len(simple_fields)}):")
        for field in simple_fields[:20]:  # Show first 20
            print(f"  - {field}")
        if len(simple_fields) > 20:
            print(f"  ... and {len(simple_fields) - 20} more")
        
        print(f"\nConditional fields ({len(conditional_fields)}):")
        for field in conditional_fields[:20]:  # Show first 20
            print(f"  - {field}")
        if len(conditional_fields) > 20:
            print(f"  ... and {len(conditional_fields) - 20} more")
        
        print(f"\nNegative conditional fields ({len(negative_fields)}):")
        for field in negative_fields[:20]:  # Show first 20
            print(f"  - {field}")
        if len(negative_fields) > 20:
            print(f"  ... and {len(negative_fields) - 20} more")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during template structure analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = analyze_template_structure()
    if success:
        print("\n🎉 Template structure analysis completed!")
        sys.exit(0)
    else:
        print("\n❌ Template structure analysis failed!")
        sys.exit(1)
