#!/usr/bin/env python3
"""
Test Word compatibility of generated documents.
"""

import sys
import os
import subprocess
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_word_compatibility():
    """Test if generated documents can be opened by Word."""
    print("=== TESTING WORD COMPATIBILITY ===\n")
    
    test_files = [
        ('Original template', 'sample_templates/onderhoudsbon_template.docx'),
        ('Simple copy', 'test_simple_copy.docx'),
        ('Minimal result', 'test_minimal_result.docx'),
        ('No signatures', 'test_no_signatures.docx'),
        ('Safe result', 'test_safe_result.docx'),
        ('Original result', 'test_original_result.docx')
    ]
    
    for name, path in test_files:
        if not os.path.exists(path):
            print(f"⚠ {name}: File not found - {path}")
            continue
            
        print(f"\n=== TESTING: {name} ===")
        print(f"File: {path}")
        print(f"Size: {os.path.getsize(path)} bytes")
        
        # Try to open with python-docx (basic validation)
        try:
            from docx import Document
            doc = Document(path)
            print(f"✓ python-docx can open: {len(doc.paragraphs)} paragraphs, {len(doc.tables)} tables")
        except Exception as e:
            print(f"✗ python-docx failed: {e}")
            continue
        
        # Try to validate with zipfile (DOCX is a ZIP)
        try:
            import zipfile
            with zipfile.ZipFile(path, 'r') as zip_file:
                # Check if all required DOCX files are present
                required_files = [
                    'word/document.xml',
                    '[Content_Types].xml',
                    '_rels/.rels'
                ]
                
                zip_contents = zip_file.namelist()
                missing_files = [f for f in required_files if f not in zip_contents]
                
                if missing_files:
                    print(f"✗ Missing required files: {missing_files}")
                else:
                    print(f"✓ ZIP structure is valid")
                    
                # Check for corruption in document.xml
                try:
                    document_xml = zip_file.read('word/document.xml')
                    print(f"✓ document.xml readable: {len(document_xml)} bytes")
                    
                    # Check if it's valid XML
                    import xml.etree.ElementTree as ET
                    ET.fromstring(document_xml)
                    print(f"✓ document.xml is valid XML")
                    
                except Exception as xml_e:
                    print(f"✗ document.xml error: {xml_e}")
                    
        except Exception as e:
            print(f"✗ ZIP validation failed: {e}")
        
        # Try to open with Word (if available)
        try:
            # Try to open with default application (Word)
            if os.name == 'nt':  # Windows
                abs_path = os.path.abspath(path)
                print(f"Attempting to open with Word: {abs_path}")
                
                # Use start command to open with default application
                result = subprocess.run(['cmd', '/c', 'start', '', abs_path], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    print(f"✓ Successfully launched with default application")
                else:
                    print(f"⚠ Launch command returned: {result.returncode}")
                    if result.stderr:
                        print(f"  Error: {result.stderr}")
                        
        except subprocess.TimeoutExpired:
            print(f"✓ Application launched (timeout is normal)")
        except Exception as e:
            print(f"⚠ Could not test with Word: {e}")
    
    print(f"\n=== DETAILED CONTENT ANALYSIS ===")
    
    # Compare the content of working vs non-working files
    try:
        from docx import Document
        
        # Load original template
        original_doc = Document('sample_templates/onderhoudsbon_template.docx')
        safe_doc = Document('test_safe_result.docx')
        
        print(f"\nOriginal template content:")
        for i, para in enumerate(original_doc.paragraphs[:5]):
            if para.text.strip():
                print(f"  Para {i}: {para.text[:100]}...")
        
        print(f"\nSafe result content:")
        for i, para in enumerate(safe_doc.paragraphs[:5]):
            if para.text.strip():
                print(f"  Para {i}: {para.text[:100]}...")
        
        # Check table content
        print(f"\nOriginal table 0, row 0:")
        if original_doc.tables:
            for i, cell in enumerate(original_doc.tables[0].rows[0].cells):
                print(f"  Cell {i}: {cell.text}")
        
        print(f"\nSafe result table 0, row 0:")
        if safe_doc.tables:
            for i, cell in enumerate(safe_doc.tables[0].rows[0].cells):
                print(f"  Cell {i}: {cell.text}")
                
    except Exception as e:
        print(f"Content analysis failed: {e}")
    
    return True

if __name__ == "__main__":
    success = test_word_compatibility()
    if success:
        print("\n🔍 Word compatibility test completed!")
        print("\nIf Word opened any files, check which ones work and which show errors.")
        sys.exit(0)
    else:
        print("\n❌ Word compatibility test failed!")
        sys.exit(1)
