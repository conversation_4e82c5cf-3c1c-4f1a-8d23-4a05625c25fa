#!/usr/bin/env python3
"""
Quick check of the existing onderhoudsbon template structure.
"""

import os
import zipfile
import xml.etree.ElementTree as ET

def analyze_template():
    template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
    
    if not os.path.exists(template_path):
        print(f"Template not found: {template_path}")
        return
    
    print(f"Analyzing: {template_path}")
    
    try:
        with zipfile.ZipFile(template_path, 'r') as docx_zip:
            # Read the main document
            document_xml = docx_zip.read('word/document.xml').decode('utf-8')
            
            print("\n=== DOCUMENT CONTENT PREVIEW ===")
            # Show first 2000 characters to get an idea of structure
            print(document_xml[:2000])
            print("..." if len(document_xml) > 2000 else "")
            
            # Look for table structures
            print(f"\n=== STRUCTURE ANALYSIS ===")
            print(f"Document length: {len(document_xml)} characters")
            print(f"Contains tables: {'<w:tbl' in document_xml}")
            print(f"Table count: {document_xml.count('<w:tbl')}")
            print(f"Contains fields: {'{' in document_xml and '}' in document_xml}")
            
            # Extract field patterns
            import re
            simple_fields = re.findall(r'\{([^{}#/^]+)\}', document_xml)
            checkbox_fields = re.findall(r'\{#([^{}]+)\}', document_xml)
            
            print(f"\n=== FIELDS FOUND ===")
            print(f"Simple fields: {len(simple_fields)}")
            for field in simple_fields[:10]:  # Show first 10
                print(f"  - {field}")
            if len(simple_fields) > 10:
                print(f"  ... and {len(simple_fields) - 10} more")
            
            print(f"\nCheckbox fields: {len(checkbox_fields)}")
            for field in checkbox_fields[:10]:  # Show first 10
                print(f"  - {field}")
            if len(checkbox_fields) > 10:
                print(f"  ... and {len(checkbox_fields) - 10} more")
            
            # Check for specific keywords that indicate structure
            keywords = ['goed', 'fout', 'nvt', 'opmerkingen', 'handtekening', 'akkoord', 'monteur']
            print(f"\n=== STRUCTURE KEYWORDS ===")
            for keyword in keywords:
                count = document_xml.lower().count(keyword)
                if count > 0:
                    print(f"  - '{keyword}': {count} occurrences")
            
    except Exception as e:
        print(f"Error analyzing template: {e}")

if __name__ == "__main__":
    analyze_template()
