#!/usr/bin/env python3
"""
Check the content of the generated document to see what happened.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_generated_document():
    """Check the generated document content."""
    print("=== Checking Generated Document ===\n")
    
    try:
        from docx import Document
        
        output_path = 'test_output_safe.docx'
        
        if not os.path.exists(output_path):
            print(f"✗ Generated document not found: {output_path}")
            return False
        
        print(f"Loading generated document: {output_path}")
        
        # Load the generated document
        doc = Document(output_path)
        
        print("✓ Generated document loaded successfully")
        print(f"Document has {len(doc.paragraphs)} paragraphs and {len(doc.tables)} tables")
        
        # Check paragraphs for template fields
        print(f"\n=== PARAGRAPHS IN GENERATED DOCUMENT ===")
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text:
                print(f"Paragraph {i}: {text}")
                if '{' in text or '}' in text:
                    print(f"  ⚠ Still contains template fields!")
        
        # Check tables for template fields and content
        print(f"\n=== TABLES IN GENERATED DOCUMENT ===")
        for table_idx, table in enumerate(doc.tables):
            print(f"\nTable {table_idx}:")
            
            for row_idx, row in enumerate(table.rows):
                row_texts = []
                has_template_fields = False
                
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_texts.append(f"[{cell_idx}]: {cell_text}")
                        if '{' in cell_text or '}' in cell_text:
                            has_template_fields = True
                
                if row_texts:
                    status = " ⚠ HAS TEMPLATE FIELDS" if has_template_fields else ""
                    print(f"    Row {row_idx}: {' | '.join(row_texts)}{status}")
        
        # Look for specific values we expected to be filled
        print(f"\n=== SEARCHING FOR EXPECTED VALUES ===")
        all_text = ""
        
        # Collect all text
        for paragraph in doc.paragraphs:
            all_text += paragraph.text + "\n"
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    all_text += cell.text + "\n"
        
        expected_values = [
            '2024-001',
            'Test Bedrijf BV',
            'Jan de Tester',
            'Piet de Monteur',
            'Accu spanning OK',
            '☑',  # Checkmark for selected items
            '☐'   # Empty checkbox for unselected items
        ]
        
        for value in expected_values:
            if value in all_text:
                print(f"  ✓ Found: '{value}'")
            else:
                print(f"  ✗ Missing: '{value}'")
        
        # Count remaining template fields
        import re
        remaining_fields = re.findall(r'\{([^}]+)\}', all_text)
        if remaining_fields:
            print(f"\n⚠ {len(remaining_fields)} template fields still remain:")
            unique_fields = list(set(remaining_fields))
            for field in unique_fields[:10]:  # Show first 10
                print(f"  - {field}")
            if len(unique_fields) > 10:
                print(f"  ... and {len(unique_fields) - 10} more")
        else:
            print(f"\n✓ No template fields remain - all were processed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error checking generated document: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_generated_document()
    if success:
        print("\n🎉 Document check completed!")
        sys.exit(0)
    else:
        print("\n❌ Document check failed!")
        sys.exit(1)
