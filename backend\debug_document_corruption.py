#!/usr/bin/env python3
"""
Debug document corruption issue step by step.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_document_corruption():
    """Debug what's causing document corruption."""
    print("=== DEBUGGING DOCUMENT CORRUPTION ===\n")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        from docx import Document
        import io
        
        service = DocumentTemplateService()
        
        # Load the template
        template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
        
        if not os.path.exists(template_path):
            print(f"✗ Template file not found: {template_path}")
            return False
        
        with open(template_path, 'rb') as f:
            template_content = f.read()
        
        print(f"✓ Template loaded: {len(template_content)} bytes")
        
        # TEST 1: Empty data (no processing)
        print(f"\n=== TEST 1: EMPTY DATA ===")
        empty_data = {}
        
        try:
            result1 = service._fill_docx_template_safe(template_content, empty_data)
            print(f"✓ Safe method with empty data: {len(result1)} bytes")
            
            # Test if it's valid
            doc1 = Document(io.BytesIO(result1))
            print(f"✓ Valid DOCX: {len(doc1.paragraphs)} paragraphs")
            
            with open('test_empty_data.docx', 'wb') as f:
                f.write(result1)
            print(f"✓ Saved: test_empty_data.docx")
            
        except Exception as e:
            print(f"✗ Safe method with empty data failed: {e}")
        
        # TEST 2: Minimal data (just text fields)
        print(f"\n=== TEST 2: MINIMAL TEXT DATA ===")
        minimal_data = {
            'bonnummer': '2024-001',
            'telefoon': '0612345678',
            'bedrijf': 'Test BV'
        }
        
        try:
            result2 = service._fill_docx_template_safe(template_content, minimal_data)
            print(f"✓ Safe method with minimal data: {len(result2)} bytes")
            
            # Test if it's valid
            doc2 = Document(io.BytesIO(result2))
            print(f"✓ Valid DOCX: {len(doc2.paragraphs)} paragraphs")
            
            with open('test_minimal_data.docx', 'wb') as f:
                f.write(result2)
            print(f"✓ Saved: test_minimal_data.docx")
            
        except Exception as e:
            print(f"✗ Safe method with minimal data failed: {e}")
            import traceback
            traceback.print_exc()
        
        # TEST 3: Boolean data only
        print(f"\n=== TEST 3: BOOLEAN DATA ONLY ===")
        boolean_data = {
            'inbraakmeldsysteem': True,
            'brandmeldsysteem': False,
            'cctv': False,
            'centrale_accu_goed': True,
            'centrale_accu_fout': False,
            'centrale_accu_nvt': False
        }
        
        try:
            result3 = service._fill_docx_template_safe(template_content, boolean_data)
            print(f"✓ Safe method with boolean data: {len(result3)} bytes")
            
            # Test if it's valid
            doc3 = Document(io.BytesIO(result3))
            print(f"✓ Valid DOCX: {len(doc3.paragraphs)} paragraphs")
            
            with open('test_boolean_data.docx', 'wb') as f:
                f.write(result3)
            print(f"✓ Saved: test_boolean_data.docx")
            
        except Exception as e:
            print(f"✗ Safe method with boolean data failed: {e}")
            import traceback
            traceback.print_exc()
        
        # TEST 4: Compare with original method
        print(f"\n=== TEST 4: ORIGINAL METHOD COMPARISON ===")
        
        try:
            result4 = service._fill_docx_template(template_content, minimal_data)
            print(f"✓ Original method with minimal data: {len(result4)} bytes")
            
            # Test if it's valid
            doc4 = Document(io.BytesIO(result4))
            print(f"✓ Valid DOCX: {len(doc4.paragraphs)} paragraphs")
            
            with open('test_original_method.docx', 'wb') as f:
                f.write(result4)
            print(f"✓ Saved: test_original_method.docx")
            
        except Exception as e:
            print(f"✗ Original method failed: {e}")
            import traceback
            traceback.print_exc()
        
        # TEST 5: Step by step safe method debugging
        print(f"\n=== TEST 5: STEP BY STEP DEBUGGING ===")
        
        try:
            # Load template with python-docx
            doc = Document(io.BytesIO(template_content))
            print(f"✓ Template loaded with python-docx")
            
            # Just save it without any changes
            output_stream = io.BytesIO()
            doc.save(output_stream)
            no_change_result = output_stream.getvalue()
            
            print(f"✓ No changes save: {len(no_change_result)} bytes")
            
            with open('test_no_changes.docx', 'wb') as f:
                f.write(no_change_result)
            print(f"✓ Saved: test_no_changes.docx")
            
            # Now try to replace just one simple field
            for paragraph in doc.paragraphs:
                if '{bonnummer}' in paragraph.text:
                    paragraph.text = paragraph.text.replace('{bonnummer}', '2024-SIMPLE')
                    break
            
            output_stream2 = io.BytesIO()
            doc.save(output_stream2)
            simple_change_result = output_stream2.getvalue()
            
            print(f"✓ Simple change save: {len(simple_change_result)} bytes")
            
            with open('test_simple_change.docx', 'wb') as f:
                f.write(simple_change_result)
            print(f"✓ Saved: test_simple_change.docx")
            
        except Exception as e:
            print(f"✗ Step by step debugging failed: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n=== SUMMARY ===")
        print(f"Generated test files:")
        print(f"  - test_empty_data.docx")
        print(f"  - test_minimal_data.docx") 
        print(f"  - test_boolean_data.docx")
        print(f"  - test_original_method.docx")
        print(f"  - test_no_changes.docx")
        print(f"  - test_simple_change.docx")
        print(f"\nTry opening each file to see which ones work and which are corrupted.")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during debugging: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_document_corruption()
    if success:
        print("\n🔍 Document corruption debugging completed!")
        sys.exit(0)
    else:
        print("\n❌ Document corruption debugging failed!")
        sys.exit(1)
