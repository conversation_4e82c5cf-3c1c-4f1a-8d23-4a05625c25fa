#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a DOCX template for the onderhoudsbon
"""

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os

def create_onderhoudsbon_template():
    """Create a DOCX template for the onderhoudsbon"""
    
    # Create a new document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Onderhoudsbon', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add basic info section
    doc.add_paragraph()
    
    # Create a table for basic information
    basic_info_table = doc.add_table(rows=4, cols=4)
    basic_info_table.style = 'Table Grid'
    
    # Fill basic info table
    cells = basic_info_table.rows[0].cells
    cells[0].text = 'Bonnummer:'
    cells[1].text = '{bonnummer}'
    cells[2].text = 'Telefoonnummer:'
    cells[3].text = '{telefoon}'
    
    cells = basic_info_table.rows[1].cells
    cells[0].text = 'Klantnummer:'
    cells[1].text = '{klantnummer}'
    cells[2].text = 'Contactpersoon:'
    cells[3].text = '{contactpersoon}'
    
    cells = basic_info_table.rows[2].cells
    cells[0].text = 'Bedrijf:'
    cells[1].text = '{bedrijf}'
    cells[2].text = 'Email:'
    cells[3].text = '{email}'
    
    cells = basic_info_table.rows[3].cells
    cells[0].text = 'Adres:'
    cells[1].text = '{adres}'
    cells[2].text = 'Type:'
    cells[3].text = '{type}'
    
    doc.add_paragraph()
    
    # Type installatie section
    doc.add_heading('Type installatie', level=2)
    type_para = doc.add_paragraph()
    type_para.add_run('{#inbraakmeldsysteem}☑{/inbraakmeldsysteem}{^inbraakmeldsysteem}☐{/inbraakmeldsysteem} Inbraakmeldsysteem    ')
    type_para.add_run('{#brandmeldsysteem}☑{/brandmeldsysteem}{^brandmeldsysteem}☐{/brandmeldsysteem} Brandmeldsysteem    ')
    type_para.add_run('{#cctv}☑{/cctv}{^cctv}☐{/cctv} CCTV')
    
    doc.add_paragraph()
    
    # Centrale / kiezer section
    doc.add_heading('Centrale / kiezer', level=2)
    centrale_table = doc.add_table(rows=6, cols=5)
    centrale_table.style = 'Table Grid'
    
    # Headers
    cells = centrale_table.rows[0].cells
    cells[0].text = 'Component'
    cells[1].text = 'Goed'
    cells[2].text = 'Fout'
    cells[3].text = 'NVT'
    cells[4].text = 'Opmerkingen'
    
    # Accu row
    cells = centrale_table.rows[1].cells
    cells[0].text = 'Accu'
    cells[1].text = '{#centrale_accu_goed}☑{/centrale_accu_goed}{^centrale_accu_goed}☐{/centrale_accu_goed}'
    cells[2].text = '{#centrale_accu_fout}☑{/centrale_accu_fout}{^centrale_accu_fout}☐{/centrale_accu_fout}'
    cells[3].text = '{#centrale_accu_nvt}☑{/centrale_accu_nvt}{^centrale_accu_nvt}☐{/centrale_accu_nvt}'
    cells[4].text = '{centrale_accu_opmerkingen}'
    
    # Voeding row
    cells = centrale_table.rows[2].cells
    cells[0].text = 'Voeding'
    cells[1].text = '{#centrale_voeding_goed}☑{/centrale_voeding_goed}{^centrale_voeding_goed}☐{/centrale_voeding_goed}'
    cells[2].text = '{#centrale_voeding_fout}☑{/centrale_voeding_fout}{^centrale_voeding_fout}☐{/centrale_voeding_fout}'
    cells[3].text = '{#centrale_voeding_nvt}☑{/centrale_voeding_nvt}{^centrale_voeding_nvt}☐{/centrale_voeding_nvt}'
    cells[4].text = '{centrale_voeding_opmerkingen}'
    
    # Lusspanning row
    cells = centrale_table.rows[3].cells
    cells[0].text = 'Lusspanning'
    cells[1].text = '{#centrale_lusspanning_goed}☑{/centrale_lusspanning_goed}{^centrale_lusspanning_goed}☐{/centrale_lusspanning_goed}'
    cells[2].text = '{#centrale_lusspanning_fout}☑{/centrale_lusspanning_fout}{^centrale_lusspanning_fout}☐{/centrale_lusspanning_fout}'
    cells[3].text = '{#centrale_lusspanning_nvt}☑{/centrale_lusspanning_nvt}{^centrale_lusspanning_nvt}☐{/centrale_lusspanning_nvt}'
    cells[4].text = '{centrale_lusspanning_opmerkingen}'
    
    # Uitlezing row
    cells = centrale_table.rows[4].cells
    cells[0].text = 'Uitlezing'
    cells[1].text = '{#centrale_uitlezing_goed}☑{/centrale_uitlezing_goed}{^centrale_uitlezing_goed}☐{/centrale_uitlezing_goed}'
    cells[2].text = '{#centrale_uitlezing_fout}☑{/centrale_uitlezing_fout}{^centrale_uitlezing_fout}☐{/centrale_uitlezing_fout}'
    cells[3].text = '{#centrale_uitlezing_nvt}☑{/centrale_uitlezing_nvt}{^centrale_uitlezing_nvt}☐{/centrale_uitlezing_nvt}'
    cells[4].text = '{centrale_uitlezing_opmerkingen}'
    
    # Algemene werking row
    cells = centrale_table.rows[5].cells
    cells[0].text = 'Algemene werking'
    cells[1].text = '{#centrale_algemeen_goed}☑{/centrale_algemeen_goed}{^centrale_algemeen_goed}☐{/centrale_algemeen_goed}'
    cells[2].text = '{#centrale_algemeen_fout}☑{/centrale_algemeen_fout}{^centrale_algemeen_fout}☐{/centrale_algemeen_fout}'
    cells[3].text = '{#centrale_algemeen_nvt}☑{/centrale_algemeen_nvt}{^centrale_algemeen_nvt}☐{/centrale_algemeen_nvt}'
    cells[4].text = '{centrale_algemeen_opmerkingen}'

    # Add detectie section
    doc.add_paragraph()
    doc.add_heading('Detectie', level=2)
    detectie_table = doc.add_table(rows=5, cols=5)
    detectie_table.style = 'Table Grid'

    # Headers
    cells = detectie_table.rows[0].cells
    cells[0].text = 'Component'
    cells[1].text = 'Goed'
    cells[2].text = 'Fout'
    cells[3].text = 'NVT'
    cells[4].text = 'Opmerkingen'

    # Bevestiging row
    cells = detectie_table.rows[1].cells
    cells[0].text = 'Bevestiging'
    cells[1].text = '{#detectie_bevestiging_goed}☑{/detectie_bevestiging_goed}{^detectie_bevestiging_goed}☐{/detectie_bevestiging_goed}'
    cells[2].text = '{#detectie_bevestiging_fout}☑{/detectie_bevestiging_fout}{^detectie_bevestiging_fout}☐{/detectie_bevestiging_fout}'
    cells[3].text = '{#detectie_bevestiging_nvt}☑{/detectie_bevestiging_nvt}{^detectie_bevestiging_nvt}☐{/detectie_bevestiging_nvt}'
    cells[4].text = '{detectie_bevestiging_opmerkingen}'

    # Werking row
    cells = detectie_table.rows[2].cells
    cells[0].text = 'Werking'
    cells[1].text = '{#detectie_werking_goed}☑{/detectie_werking_goed}{^detectie_werking_goed}☐{/detectie_werking_goed}'
    cells[2].text = '{#detectie_werking_fout}☑{/detectie_werking_fout}{^detectie_werking_fout}☐{/detectie_werking_fout}'
    cells[3].text = '{#detectie_werking_nvt}☑{/detectie_werking_nvt}{^detectie_werking_nvt}☐{/detectie_werking_nvt}'
    cells[4].text = '{detectie_werking_opmerkingen}'

    # Projectie row
    cells = detectie_table.rows[3].cells
    cells[0].text = 'Projectie'
    cells[1].text = '{#detectie_projectie_goed}☑{/detectie_projectie_goed}{^detectie_projectie_goed}☐{/detectie_projectie_goed}'
    cells[2].text = '{#detectie_projectie_fout}☑{/detectie_projectie_fout}{^detectie_projectie_fout}☐{/detectie_projectie_fout}'
    cells[3].text = '{#detectie_projectie_nvt}☑{/detectie_projectie_nvt}{^detectie_projectie_nvt}☐{/detectie_projectie_nvt}'
    cells[4].text = '{detectie_projectie_opmerkingen}'

    # Detectie algemeen row
    cells = detectie_table.rows[4].cells
    cells[0].text = 'Detectie algemeen'
    cells[1].text = '{#detectie_algemeen_goed}☑{/detectie_algemeen_goed}{^detectie_algemeen_goed}☐{/detectie_algemeen_goed}'
    cells[2].text = '{#detectie_algemeen_fout}☑{/detectie_algemeen_fout}{^detectie_algemeen_fout}☐{/detectie_algemeen_fout}'
    cells[3].text = '{#detectie_algemeen_nvt}☑{/detectie_algemeen_nvt}{^detectie_algemeen_nvt}☐{/detectie_algemeen_nvt}'
    cells[4].text = '{detectie_algemeen_opmerkingen}'

    # Add bekabeling section
    doc.add_heading('Bekabeling', level=2)
    bekabeling_table = doc.add_table(rows=3, cols=5)
    bekabeling_table.style = 'Table Grid'

    # Headers
    cells = bekabeling_table.rows[0].cells
    cells[0].text = 'Component'
    cells[1].text = 'Goed'
    cells[2].text = 'Fout'
    cells[3].text = 'NVT'
    cells[4].text = 'Opmerkingen'

    # Bevestiging row
    cells = bekabeling_table.rows[1].cells
    cells[0].text = 'Bevestiging'
    cells[1].text = '{#bekabeling_bevestiging_goed}☑{/bekabeling_bevestiging_goed}{^bekabeling_bevestiging_goed}☐{/bekabeling_bevestiging_goed}'
    cells[2].text = '{#bekabeling_bevestiging_fout}☑{/bekabeling_bevestiging_fout}{^bekabeling_bevestiging_fout}☐{/bekabeling_bevestiging_fout}'
    cells[3].text = '{#bekabeling_bevestiging_nvt}☑{/bekabeling_bevestiging_nvt}{^bekabeling_bevestiging_nvt}☐{/bekabeling_bevestiging_nvt}'
    cells[4].text = '{bekabeling_bevestiging_opmerkingen}'

    # Afscherming row
    cells = bekabeling_table.rows[2].cells
    cells[0].text = 'Afscherming'
    cells[1].text = '{#bekabeling_afscherming_goed}☑{/bekabeling_afscherming_goed}{^bekabeling_afscherming_goed}☐{/bekabeling_afscherming_goed}'
    cells[2].text = '{#bekabeling_afscherming_fout}☑{/bekabeling_afscherming_fout}{^bekabeling_afscherming_fout}☐{/bekabeling_afscherming_fout}'
    cells[3].text = '{#bekabeling_afscherming_nvt}☑{/bekabeling_afscherming_nvt}{^bekabeling_afscherming_nvt}☐{/bekabeling_afscherming_nvt}'
    cells[4].text = '{bekabeling_afscherming_opmerkingen}'

    # Add signalering section
    doc.add_paragraph()
    doc.add_heading('Signalering', level=2)
    signalering_table = doc.add_table(rows=5, cols=5)
    signalering_table.style = 'Table Grid'

    # Headers
    cells = signalering_table.rows[0].cells
    cells[0].text = 'Component'
    cells[1].text = 'Goed'
    cells[2].text = 'Fout'
    cells[3].text = 'NVT'
    cells[4].text = 'Opmerkingen'

    # Bevestiging row
    cells = signalering_table.rows[1].cells
    cells[0].text = 'Bevestiging'
    cells[1].text = '{#signalering_bevestiging_goed}☑{/signalering_bevestiging_goed}{^signalering_bevestiging_goed}☐{/signalering_bevestiging_goed}'
    cells[2].text = '{#signalering_bevestiging_fout}☑{/signalering_bevestiging_fout}{^signalering_bevestiging_fout}☐{/signalering_bevestiging_fout}'
    cells[3].text = '{#signalering_bevestiging_nvt}☑{/signalering_bevestiging_nvt}{^signalering_bevestiging_nvt}☐{/signalering_bevestiging_nvt}'
    cells[4].text = '{signalering_bevestiging_opmerkingen}'

    # Werking flits row
    cells = signalering_table.rows[2].cells
    cells[0].text = 'Werking flits'
    cells[1].text = '{#signalering_werking_flits_goed}☑{/signalering_werking_flits_goed}{^signalering_werking_flits_goed}☐{/signalering_werking_flits_goed}'
    cells[2].text = '{#signalering_werking_flits_fout}☑{/signalering_werking_flits_fout}{^signalering_werking_flits_fout}☐{/signalering_werking_flits_fout}'
    cells[3].text = '{#signalering_werking_flits_nvt}☑{/signalering_werking_flits_nvt}{^signalering_werking_flits_nvt}☐{/signalering_werking_flits_nvt}'
    cells[4].text = '{signalering_werking_flits_opmerkingen}'

    # Werking sirene row
    cells = signalering_table.rows[3].cells
    cells[0].text = 'Werking sirene'
    cells[1].text = '{#signalering_werking_sirene_goed}☑{/signalering_werking_sirene_goed}{^signalering_werking_sirene_goed}☐{/signalering_werking_sirene_goed}'
    cells[2].text = '{#signalering_werking_sirene_fout}☑{/signalering_werking_sirene_fout}{^signalering_werking_sirene_fout}☐{/signalering_werking_sirene_fout}'
    cells[3].text = '{#signalering_werking_sirene_nvt}☑{/signalering_werking_sirene_nvt}{^signalering_werking_sirene_nvt}☐{/signalering_werking_sirene_nvt}'
    cells[4].text = '{signalering_werking_sirene_opmerkingen}'

    # Signalering algemeen row
    cells = signalering_table.rows[4].cells
    cells[0].text = 'Signalering algemeen'
    cells[1].text = '{#signalering_algemeen_goed}☑{/signalering_algemeen_goed}{^signalering_algemeen_goed}☐{/signalering_algemeen_goed}'
    cells[2].text = '{#signalering_algemeen_fout}☑{/signalering_algemeen_fout}{^signalering_algemeen_fout}☐{/signalering_algemeen_fout}'
    cells[3].text = '{#signalering_algemeen_nvt}☑{/signalering_algemeen_nvt}{^signalering_algemeen_nvt}☐{/signalering_algemeen_nvt}'
    cells[4].text = '{signalering_algemeen_opmerkingen}'

    # Add doormelding section
    doc.add_heading('Doormelding', level=2)
    doormelding_table = doc.add_table(rows=7, cols=5)
    doormelding_table.style = 'Table Grid'

    # Headers
    cells = doormelding_table.rows[0].cells
    cells[0].text = 'Component'
    cells[1].text = 'Goed'
    cells[2].text = 'Fout'
    cells[3].text = 'NVT'
    cells[4].text = 'Opmerkingen'

    # Schakelmelding row
    cells = doormelding_table.rows[1].cells
    cells[0].text = 'Schakelmelding'
    cells[1].text = '{#doormelding_schakel_goed}☑{/doormelding_schakel_goed}{^doormelding_schakel_goed}☐{/doormelding_schakel_goed}'
    cells[2].text = '{#doormelding_schakel_fout}☑{/doormelding_schakel_fout}{^doormelding_schakel_fout}☐{/doormelding_schakel_fout}'
    cells[3].text = '{#doormelding_schakel_nvt}☑{/doormelding_schakel_nvt}{^doormelding_schakel_nvt}☐{/doormelding_schakel_nvt}'
    cells[4].text = '{doormelding_schakel_opmerkingen}'

    # Inbraak row
    cells = doormelding_table.rows[2].cells
    cells[0].text = 'Inbraak'
    cells[1].text = '{#doormelding_inbraak_goed}☑{/doormelding_inbraak_goed}{^doormelding_inbraak_goed}☐{/doormelding_inbraak_goed}'
    cells[2].text = '{#doormelding_inbraak_fout}☑{/doormelding_inbraak_fout}{^doormelding_inbraak_fout}☐{/doormelding_inbraak_fout}'
    cells[3].text = '{#doormelding_inbraak_nvt}☑{/doormelding_inbraak_nvt}{^doormelding_inbraak_nvt}☐{/doormelding_inbraak_nvt}'
    cells[4].text = '{doormelding_inbraak_opmerkingen}'

    # Overval row
    cells = doormelding_table.rows[3].cells
    cells[0].text = 'Overval'
    cells[1].text = '{#doormelding_overval_goed}☑{/doormelding_overval_goed}{^doormelding_overval_goed}☐{/doormelding_overval_goed}'
    cells[2].text = '{#doormelding_overval_fout}☑{/doormelding_overval_fout}{^doormelding_overval_fout}☐{/doormelding_overval_fout}'
    cells[3].text = '{#doormelding_overval_nvt}☑{/doormelding_overval_nvt}{^doormelding_overval_nvt}☐{/doormelding_overval_nvt}'
    cells[4].text = '{doormelding_overval_opmerkingen}'

    # Brand row
    cells = doormelding_table.rows[4].cells
    cells[0].text = 'Brand'
    cells[1].text = '{#doormelding_brand_goed}☑{/doormelding_brand_goed}{^doormelding_brand_goed}☐{/doormelding_brand_goed}'
    cells[2].text = '{#doormelding_brand_fout}☑{/doormelding_brand_fout}{^doormelding_brand_fout}☐{/doormelding_brand_fout}'
    cells[3].text = '{#doormelding_brand_nvt}☑{/doormelding_brand_nvt}{^doormelding_brand_nvt}☐{/doormelding_brand_nvt}'
    cells[4].text = '{doormelding_brand_opmerkingen}'

    # Technisch row
    cells = doormelding_table.rows[5].cells
    cells[0].text = 'Technisch'
    cells[1].text = '{#doormelding_technisch_goed}☑{/doormelding_technisch_goed}{^doormelding_technisch_goed}☐{/doormelding_technisch_goed}'
    cells[2].text = '{#doormelding_technisch_fout}☑{/doormelding_technisch_fout}{^doormelding_technisch_fout}☐{/doormelding_technisch_fout}'
    cells[3].text = '{#doormelding_technisch_nvt}☑{/doormelding_technisch_nvt}{^doormelding_technisch_nvt}☐{/doormelding_technisch_nvt}'
    cells[4].text = '{doormelding_technisch_opmerkingen}'

    # Contact gewenst MK row
    cells = doormelding_table.rows[6].cells
    cells[0].text = 'Contact gewenst MK'
    cells[1].text = '{#doormelding_contact_goed}☑{/doormelding_contact_goed}{^doormelding_contact_goed}☐{/doormelding_contact_goed}'
    cells[2].text = '{#doormelding_contact_fout}☑{/doormelding_contact_fout}{^doormelding_contact_fout}☐{/doormelding_contact_fout}'
    cells[3].text = '{#doormelding_contact_nvt}☑{/doormelding_contact_nvt}{^doormelding_contact_nvt}☐{/doormelding_contact_nvt}'
    cells[4].text = '{doormelding_contact_opmerkingen}'

    # Add algemene staat section
    doc.add_paragraph()
    doc.add_heading('Algemene staat', level=2)

    # Installatie status checkboxes
    doc.add_paragraph('{#installatie_in_orde}☑{/installatie_in_orde}{^installatie_in_orde}☐{/installatie_in_orde} Installatie in orde')
    doc.add_paragraph('{#installatie_niet_in_orde}☑{/installatie_niet_in_orde}{^installatie_niet_in_orde}☐{/installatie_niet_in_orde} Installatie niet in orde')

    # Opmerkingen
    doc.add_paragraph('Ja, opmerking: {ja_opmerking}')
    doc.add_paragraph('Nee / onbekend: {nee_onbekend}')

    doc.add_paragraph()

    # Final section with signatures
    doc.add_heading('Akkoord', level=2)
    signature_table = doc.add_table(rows=4, cols=2)
    signature_table.style = 'Table Grid'
    
    cells = signature_table.rows[0].cells
    cells[0].text = 'Akkoord opdrachtgever'
    cells[1].text = 'Monteur'
    
    cells = signature_table.rows[1].cells
    cells[0].text = 'Naam: {klant_naam}'
    cells[1].text = 'Naam: {monteur_naam}'
    
    cells = signature_table.rows[2].cells
    cells[0].text = 'Datum: {datum}'
    cells[1].text = 'Begin/eindtijd: {begin_tijd} - {eind_tijd}'
    
    cells = signature_table.rows[3].cells
    cells[0].text = 'Handtekening: {klant_handtekening}'
    cells[1].text = 'Handtekening: {monteur_handtekening}'
    
    # Save the document
    output_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    doc.save(output_path)
    
    print(f"✓ Onderhoudsbon template created: {output_path}")
    return output_path

if __name__ == "__main__":
    create_onderhoudsbon_template()
