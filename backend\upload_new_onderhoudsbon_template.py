#!/usr/bin/env python3
"""
Upload the new complete onderhoudsbon template to the database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def upload_new_template():
    """Upload the new onderhoudsbon template to the database."""
    print("=== UPLOADING NEW ONDERHOUDSBON TEMPLATE ===\n")
    
    try:
        from app import create_app
        from app.services.document_template_service import DocumentTemplateService
        from app.repositories.document_template_repository import DocumentTemplateRepository
        from app.models.user import User
        from werkzeug.datastructures import FileStorage
        import io
        
        app, _ = create_app()
        
        with app.app_context():
            service = DocumentTemplateService()
            repo = DocumentTemplateRepository()
            
            # Load the new template file
            template_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
            
            if not os.path.exists(template_path):
                print(f"✗ Template file not found: {template_path}")
                return False
            
            print(f"Loading template: {template_path}")
            
            # Read the template file
            with open(template_path, 'rb') as f:
                template_content = f.read()
            
            print(f"✓ Template loaded: {len(template_content)} bytes")
            
            # Check if onderhoudsbon template already exists
            existing_templates = repo.get_all()
            onderhoudsbon_template = None
            
            for template in existing_templates:
                if template.name.lower() == 'onderhoudsbon' or 'onderhoudsbon' in template.name.lower():
                    onderhoudsbon_template = template
                    break
            
            # Get admin user (assuming user ID 1 is admin)
            admin_user = User.query.filter_by(id=1).first()
            if not admin_user:
                print("✗ Admin user not found (ID=1)")
                return False
            
            # Create FileStorage object from the template content
            file_obj = io.BytesIO(template_content)
            file_storage = FileStorage(
                stream=file_obj,
                filename='onderhoudsbon_template.docx',
                content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            )
            
            if onderhoudsbon_template:
                print(f"✓ Found existing onderhoudsbon template: {onderhoudsbon_template.name}")
                print("✓ Updating existing template with new content...")
                
                # Delete the old template and create a new one
                # (Since we can't easily update the file content, we'll replace it)
                repo.delete(onderhoudsbon_template.id)
                print("✓ Deleted old template")
                
            # Create new template
            template_data = {
                'name': 'Onderhoudsbon',
                'document_type': 'onderhoudsbon',
                'description': 'Complete onderhoudsbon template with all sections: Centrale/kiezer, Detectie, Bekabeling, Signalering, Doormelding, Algemene staat, and Handtekeningen',
                'created_by': admin_user.id
            }
            
            print("✓ Creating new template...")
            new_template = service.create_template(template_data, file_storage)
            
            print(f"✓ Template uploaded successfully!")
            print(f"   ID: {new_template['id']}")
            print(f"   Name: {new_template['name']}")
            print(f"   Document Type: {new_template['document_type']}")
            print(f"   File Type: {new_template['file_type']}")
            print(f"   Created By: {new_template['created_by']}")
            
            # Test template analysis
            print("\n=== TESTING TEMPLATE ANALYSIS ===")
            try:
                analysis = service.analyze_template_structure(new_template['id'])
                print(f"✓ Template analysis successful!")
                print(f"   Fields found: {len(analysis.get('fields', []))}")
                print(f"   Checkboxes found: {len(analysis.get('checkboxes', []))}")
                print(f"   Tables found: {len(analysis.get('tables', []))}")
                print(f"   Has structured tables: {analysis.get('has_structured_tables', False)}")
                
                # Show some sample fields
                if analysis.get('fields'):
                    print(f"   Sample fields: {[f['name'] for f in analysis['fields'][:5]]}")
                if analysis.get('checkboxes'):
                    print(f"   Sample checkboxes: {[c['name'] for c in analysis['checkboxes'][:5]]}")
                    
            except Exception as e:
                print(f"✗ Template analysis failed: {e}")
            
            return True
            
    except Exception as e:
        print(f"✗ Failed to upload template: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = upload_new_template()
    if success:
        print("\n🎉 SUCCESS: New onderhoudsbon template uploaded!")
        print("   You can now test it in the frontend.")
    else:
        print("\n❌ FAILED: Could not upload template.")
        sys.exit(1)
